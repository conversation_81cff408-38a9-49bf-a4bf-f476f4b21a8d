/* 3D Viewer Specific Styles */

/* Three.js Container Enhancements */
.three-js-container {
    position: relative;
    overflow: hidden;
    border-radius: var(--radius-md);
}

.three-js-container canvas {
    display: block;
    width: 100% !important;
    height: 100% !important;
}

/* 3D Controls Overlay */
.viewer-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 10;
}

.overlay-controls {
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-md);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    pointer-events: auto;
}

.overlay-btn {
    width: 40px;
    height: 40px;
    background: rgba(0, 0, 0, 0.7);
    border: none;
    border-radius: 50%;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-fast);
    backdrop-filter: blur(4px);
}

.overlay-btn:hover {
    background: rgba(0, 0, 0, 0.9);
    transform: scale(1.1);
}

.overlay-btn i {
    font-size: 1rem;
}

/* Loading Indicator for 3D Models */
.model-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: white;
    z-index: 20;
}

.model-loading .spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-md);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 3D Model Information Panel */
.model-info-panel {
    position: absolute;
    bottom: var(--spacing-md);
    left: var(--spacing-md);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    backdrop-filter: blur(4px);
    pointer-events: auto;
    min-width: 200px;
}

.model-info-panel h4 {
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    color: var(--accent-color);
}

.info-item {
    display: flex;
    justify-content: space-between;
    font-size: 0.75rem;
    margin-bottom: var(--spacing-xs);
}

.info-item:last-child {
    margin-bottom: 0;
}

.info-label {
    color: rgba(255, 255, 255, 0.8);
}

.info-value {
    color: white;
    font-weight: 500;
}

/* Segmentation Tools Overlay */
.segmentation-overlay {
    position: absolute;
    top: var(--spacing-md);
    left: var(--spacing-md);
    display: flex;
    gap: var(--spacing-sm);
    pointer-events: auto;
}

.seg-tool-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    background: rgba(0, 0, 0, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    backdrop-filter: blur(4px);
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.seg-tool-btn:hover {
    background: rgba(0, 0, 0, 0.9);
    border-color: var(--primary-color);
}

.seg-tool-btn.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

/* Measurement Tools */
.measurement-line {
    position: absolute;
    background: var(--accent-color);
    height: 2px;
    transform-origin: left center;
    pointer-events: none;
    z-index: 15;
}

.measurement-point {
    position: absolute;
    width: 8px;
    height: 8px;
    background: var(--accent-color);
    border: 2px solid white;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    pointer-events: none;
    z-index: 16;
}

.measurement-label {
    position: absolute;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
    pointer-events: none;
    z-index: 17;
    backdrop-filter: blur(4px);
}

/* Annotation System */
.annotation-marker {
    position: absolute;
    width: 20px;
    height: 20px;
    background: var(--danger-color);
    border: 2px solid white;
    border-radius: 50%;
    cursor: pointer;
    transform: translate(-50%, -50%);
    z-index: 18;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.75rem;
    font-weight: bold;
    box-shadow: var(--shadow-md);
}

.annotation-marker:hover {
    transform: translate(-50%, -50%) scale(1.2);
}

.annotation-popup {
    position: absolute;
    background: white;
    border: 1px solid var(--bg-tertiary);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    box-shadow: var(--shadow-lg);
    z-index: 19;
    min-width: 200px;
    max-width: 300px;
}

.annotation-popup::before {
    content: '';
    position: absolute;
    top: -8px;
    left: 20px;
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid white;
}

.annotation-content {
    font-size: 0.875rem;
    color: var(--text-primary);
    line-height: 1.4;
}

.annotation-meta {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin-top: var(--spacing-sm);
    padding-top: var(--spacing-sm);
    border-top: 1px solid var(--bg-tertiary);
}

/* Cross-section Views */
.cross-section-container {
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-md);
    width: 150px;
    height: 150px;
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: var(--radius-md);
    overflow: hidden;
    pointer-events: auto;
    backdrop-filter: blur(4px);
}

.cross-section-canvas {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.cross-section-label {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    text-align: center;
    padding: var(--spacing-xs);
    font-size: 0.75rem;
    font-weight: 500;
}

/* Volume Rendering Controls */
.volume-controls {
    position: absolute;
    bottom: var(--spacing-md);
    right: var(--spacing-md);
    background: rgba(0, 0, 0, 0.8);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    backdrop-filter: blur(4px);
    pointer-events: auto;
    min-width: 200px;
}

.volume-controls h5 {
    color: white;
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
}

.volume-slider-group {
    margin-bottom: var(--spacing-sm);
}

.volume-slider-group:last-child {
    margin-bottom: 0;
}

.volume-slider-label {
    display: flex;
    justify-content: space-between;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.75rem;
    margin-bottom: var(--spacing-xs);
}

.volume-slider {
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: var(--radius-sm);
    outline: none;
    cursor: pointer;
}

.volume-slider::-webkit-slider-thumb {
    appearance: none;
    width: 14px;
    height: 14px;
    background: var(--primary-color);
    border-radius: 50%;
    cursor: pointer;
}

.volume-slider::-moz-range-thumb {
    width: 14px;
    height: 14px;
    background: var(--primary-color);
    border-radius: 50%;
    cursor: pointer;
    border: none;
}

/* Performance Monitor */
.performance-monitor {
    position: absolute;
    top: var(--spacing-md);
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    font-size: 0.75rem;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    backdrop-filter: blur(4px);
    pointer-events: none;
}

.fps-counter {
    color: var(--secondary-color);
    font-weight: 600;
}

.memory-usage {
    color: var(--accent-color);
    margin-left: var(--spacing-md);
}

/* Fullscreen Mode */
.viewer-3d.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9999;
    background: #000;
}

.viewer-3d.fullscreen .three-js-container {
    height: 100vh;
}

.viewer-3d.fullscreen .viewer-controls {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(4px);
}

/* VR/AR Mode Indicators */
.vr-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    text-align: center;
    backdrop-filter: blur(4px);
    z-index: 30;
}

.vr-indicator i {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.vr-indicator h4 {
    font-size: 1.125rem;
    margin-bottom: var(--spacing-sm);
}

.vr-indicator p {
    font-size: 0.875rem;
    color: rgba(255, 255, 255, 0.8);
}

/* Responsive 3D Viewer */
@media (max-width: 768px) {
    .model-info-panel,
    .volume-controls {
        position: relative;
        margin-top: var(--spacing-md);
        background: var(--bg-secondary);
        color: var(--text-primary);
        backdrop-filter: none;
    }
    
    .overlay-controls {
        flex-direction: row;
        top: auto;
        bottom: var(--spacing-md);
        right: var(--spacing-md);
    }
    
    .cross-section-container {
        width: 100px;
        height: 100px;
    }
    
    .segmentation-overlay {
        flex-wrap: wrap;
    }
    
    .seg-tool-btn {
        font-size: 0.75rem;
        padding: var(--spacing-xs) var(--spacing-sm);
    }
}

/* Dark Mode Adjustments */
@media (prefers-color-scheme: dark) {
    .annotation-popup {
        background: var(--dark-bg-secondary);
        border-color: var(--dark-bg-tertiary);
        color: var(--dark-text-primary);
    }
    
    .annotation-popup::before {
        border-bottom-color: var(--dark-bg-secondary);
    }
    
    .annotation-meta {
        color: var(--dark-text-secondary);
        border-color: var(--dark-bg-tertiary);
    }
}

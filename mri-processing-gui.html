<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MRI Brain Image Processing & Analysis GUI</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/plotly.js/2.26.0/plotly.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r150/three.min.js"></script>
    
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --ml-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --dl-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            --warning-gradient: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            --error-gradient: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            
            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-border: rgba(255, 255, 255, 0.2);
            --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
            
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --text-light: #94a3b8;
            
            --border-radius: 16px;
            --border-radius-lg: 24px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }
        
        /* Animated Background */
        .animated-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background: var(--primary-gradient);
        }
        
        .animated-bg::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            animation: float 20s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(1deg); }
        }
        
        /* Glass Morphism Effects */
        .glass-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            box-shadow: var(--glass-shadow);
        }
        
        .glass-card-lg {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            border: 1px solid rgba(255, 255, 255, 0.25);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 12px 40px 0 rgba(31, 38, 135, 0.4);
        }
        
        /* Navigation Breadcrumb */
        .nav-breadcrumb {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 15px 20px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .breadcrumb {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 0.9rem;
            color: var(--text-secondary);
        }
        
        .breadcrumb a {
            color: #2563eb;
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
            padding: 4px 8px;
            border-radius: 6px;
        }
        
        .breadcrumb a:hover {
            background: rgba(37, 99, 235, 0.1);
            color: #1d4ed8;
        }
        
        /* Header */
        .header {
            position: relative;
            padding: 60px 20px;
            text-align: center;
            overflow: hidden;
        }
        
        .header-content {
            position: relative;
            z-index: 2;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header h1 {
            font-size: clamp(2rem, 4vw, 3.5rem);
            font-weight: 800;
            margin-bottom: 15px;
            color: white;
            text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            letter-spacing: -0.02em;
        }
        
        .header .subtitle {
            font-size: clamp(1rem, 2vw, 1.2rem);
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 30px;
            font-weight: 400;
            max-width: 700px;
            margin-left: auto;
            margin-right: auto;
        }
        
        /* Container */
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 30px 20px;
        }
        
        /* Main Layout */
        .main-layout {
            display: grid;
            grid-template-columns: 400px 1fr 350px;
            gap: 30px;
            margin-bottom: 40px;
        }
        
        /* Upload Panel */
        .upload-panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius-lg);
            padding: 30px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            height: fit-content;
            position: sticky;
            top: 100px;
        }
        
        .upload-area {
            border: 3px dashed #cbd5e1;
            border-radius: 12px;
            padding: 40px 20px;
            text-align: center;
            transition: var(--transition);
            cursor: pointer;
            margin-bottom: 25px;
            background: linear-gradient(135deg, #f8fafc, #f1f5f9);
        }
        
        .upload-area:hover {
            border-color: #3b82f6;
            background: linear-gradient(135deg, #dbeafe, #bfdbfe);
            transform: translateY(-2px);
        }
        
        .upload-area.dragover {
            border-color: #10b981;
            background: linear-gradient(135deg, #dcfce7, #bbf7d0);
            transform: scale(1.02);
        }
        
        .upload-icon {
            font-size: 3rem;
            color: #64748b;
            margin-bottom: 15px;
            transition: var(--transition);
        }
        
        .upload-area:hover .upload-icon {
            color: #3b82f6;
            transform: scale(1.1);
        }
        
        .upload-text {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 8px;
        }
        
        .upload-subtext {
            font-size: 0.9rem;
            color: var(--text-secondary);
        }
        
        .file-input {
            display: none;
        }
        
        .uploaded-file {
            background: linear-gradient(135deg, #dcfce7, #bbf7d0);
            border: 1px solid #22c55e;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            display: none;
        }
        
        .file-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .file-icon {
            color: #22c55e;
            font-size: 1.2rem;
        }
        
        .file-details {
            flex: 1;
        }
        
        .file-name {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 2px;
        }
        
        .file-size {
            font-size: 0.8rem;
            color: var(--text-secondary);
        }
        
        /* Processing Options */
        .processing-options {
            background: #f8fafc;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 25px;
        }
        
        .option-group {
            margin-bottom: 20px;
        }
        
        .option-label {
            display: block;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 10px;
        }
        
        .method-selection {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .method-option {
            background: white;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: var(--transition);
        }
        
        .method-option:hover {
            border-color: #3b82f6;
            transform: translateY(-2px);
        }
        
        .method-option.selected {
            border-color: #3b82f6;
            background: linear-gradient(135deg, #dbeafe, #bfdbfe);
        }
        
        .method-option.ml {
            border-color: #f093fb;
        }
        
        .method-option.ml.selected {
            background: var(--ml-gradient);
            color: white;
        }
        
        .method-option.dl {
            border-color: #4facfe;
        }
        
        .method-option.dl.selected {
            background: var(--dl-gradient);
            color: white;
        }
        
        .method-icon {
            font-size: 1.5rem;
            margin-bottom: 8px;
        }
        
        .method-name {
            font-weight: 600;
            font-size: 0.9rem;
        }
        
        /* Buttons */
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            font-size: 0.95rem;
            text-decoration: none;
            position: relative;
            overflow: hidden;
            width: 100%;
        }
        
        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }
        
        .btn:hover::before {
            left: 100%;
        }
        
        .btn-primary {
            background: var(--primary-gradient);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }
        
        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.5);
        }
        
        .btn-primary:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .btn-secondary {
            background: var(--success-gradient);
            color: white;
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
        }
        
        .btn-secondary:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.5);
        }
        
        /* Processing Panel */
        .processing-panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius-lg);
            padding: 30px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            min-height: 600px;
        }
        
        .processing-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f1f5f9;
        }
        
        .processing-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            background: var(--primary-gradient);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
        }
        
        .processing-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
        }
        
        /* Image Display */
        .image-display {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 25px;
        }
        
        .image-container {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 15px;
            text-align: center;
            min-height: 200px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        
        .image-placeholder {
            color: #94a3b8;
            font-size: 3rem;
            margin-bottom: 10px;
        }
        
        .image-label {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 5px;
        }
        
        .image-description {
            font-size: 0.8rem;
            color: var(--text-secondary);
        }
        
        .uploaded-image {
            max-width: 100%;
            max-height: 180px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        /* Progress Bar */
        .progress-container {
            margin: 25px 0;
        }
        
        .progress-label {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .progress-text {
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .progress-percentage {
            font-weight: 600;
            color: #3b82f6;
        }
        
        .progress-bar {
            background: #e2e8f0;
            border-radius: 50px;
            height: 8px;
            overflow: hidden;
            position: relative;
        }
        
        .progress-fill {
            background: var(--primary-gradient);
            height: 100%;
            border-radius: 50px;
            transition: width 0.3s ease;
            width: 0%;
            position: relative;
        }
        
        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            animation: shimmer 2s infinite;
        }
        
        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }
        
        /* Results Panel */
        .results-panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius-lg);
            padding: 30px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            height: fit-content;
            position: sticky;
            top: 100px;
        }
        
        .results-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .results-title {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--text-primary);
        }
        
        /* Metrics Grid */
        .metrics-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .metric-card {
            background: linear-gradient(135deg, #f8fafc, #f1f5f9);
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 15px;
            text-align: center;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        
        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--primary-gradient);
        }
        
        .metric-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .metric-value {
            font-size: 1.5rem;
            font-weight: 800;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 5px;
        }
        
        .metric-label {
            font-size: 0.8rem;
            color: var(--text-secondary);
            font-weight: 500;
        }
        
        /* Comparison Chart */
        .comparison-section {
            margin-top: 25px;
        }
        
        .comparison-title {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .comparison-chart {
            height: 200px;
            background: white;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }
        
        /* Status Messages */
        .status-message {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 500;
        }
        
        .status-message.info {
            background: linear-gradient(135deg, #dbeafe, #bfdbfe);
            color: #1e40af;
            border: 1px solid #3b82f6;
        }
        
        .status-message.success {
            background: linear-gradient(135deg, #dcfce7, #bbf7d0);
            color: #166534;
            border: 1px solid #22c55e;
        }
        
        .status-message.warning {
            background: linear-gradient(135deg, #fef3c7, #fde68a);
            color: #92400e;
            border: 1px solid #f59e0b;
        }
        
        .status-message.error {
            background: linear-gradient(135deg, #fee2e2, #fecaca);
            color: #dc2626;
            border: 1px solid #ef4444;
        }
        
        /* Loading Animation */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(59, 130, 246, 0.3);
            border-radius: 50%;
            border-top-color: #3b82f6;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        /* Responsive Design */
        @media (max-width: 1200px) {
            .main-layout {
                grid-template-columns: 1fr;
                gap: 25px;
            }
            
            .upload-panel,
            .results-panel {
                position: static;
            }
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 20px 15px;
            }
            
            .upload-panel,
            .processing-panel,
            .results-panel {
                padding: 20px;
            }
            
            .image-display {
                grid-template-columns: 1fr;
            }
            
            .method-selection {
                grid-template-columns: 1fr;
            }
            
            .metrics-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="animated-bg"></div>
    
    <!-- Navigation Breadcrumb -->
    <div class="nav-breadcrumb">
        <div class="breadcrumb">
            <a href="index.html"><i class="fas fa-home"></i> Home</a>
            <i class="fas fa-chevron-right"></i>
            <a href="platform.html">Platform</a>
            <i class="fas fa-chevron-right"></i>
            <span>MRI Processing GUI</span>
        </div>
    </div>

    <!-- Header -->
    <div class="header">
        <div class="header-content">
            <h1><i class="fas fa-brain"></i> MRI Brain Image Processing & Analysis GUI</h1>
            <p class="subtitle">Upload MRI brain images for advanced 3D segmentation and analysis using both Deep Learning and Machine Learning methods with real-time performance comparison</p>
        </div>
    </div>

    <div class="container">
        <!-- Main Layout -->
        <div class="main-layout">
            <!-- Upload Panel -->
            <div class="upload-panel">
                <h3 style="margin-bottom: 20px; display: flex; align-items: center; gap: 10px;">
                    <i class="fas fa-upload"></i> Upload MRI Image
                </h3>

                <!-- File Upload Area -->
                <div class="upload-area" id="upload-area">
                    <div class="upload-icon">
                        <i class="fas fa-cloud-upload-alt"></i>
                    </div>
                    <div class="upload-text">Drop MRI files here</div>
                    <div class="upload-subtext">or click to browse</div>
                    <input type="file" id="file-input" class="file-input" accept=".dcm,.nii,.nii.gz,.img,.hdr" multiple>
                </div>

                <!-- Uploaded File Display -->
                <div class="uploaded-file" id="uploaded-file">
                    <div class="file-info">
                        <div class="file-icon">
                            <i class="fas fa-file-medical"></i>
                        </div>
                        <div class="file-details">
                            <div class="file-name" id="file-name">brain_mri.nii</div>
                            <div class="file-size" id="file-size">2.4 MB</div>
                        </div>
                        <button onclick="removeFile()" style="background: none; border: none; color: #ef4444; cursor: pointer;">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>

                <!-- Processing Options -->
                <div class="processing-options">
                    <div class="option-group">
                        <label class="option-label">
                            <i class="fas fa-cogs"></i> Processing Methods
                        </label>
                        <div class="method-selection">
                            <div class="method-option ml selected" data-method="ml">
                                <div class="method-icon">
                                    <i class="fas fa-project-diagram"></i>
                                </div>
                                <div class="method-name">Machine Learning</div>
                            </div>
                            <div class="method-option dl selected" data-method="dl">
                                <div class="method-icon">
                                    <i class="fas fa-brain"></i>
                                </div>
                                <div class="method-name">Deep Learning</div>
                            </div>
                        </div>
                    </div>

                    <div class="option-group">
                        <label class="option-label">
                            <i class="fas fa-sliders-h"></i> Quality vs Speed
                        </label>
                        <input type="range" id="quality-slider" min="1" max="5" value="3" style="width: 100%; margin-bottom: 10px;">
                        <div style="display: flex; justify-content: space-between; font-size: 0.8rem; color: var(--text-secondary);">
                            <span>⚡ Fastest</span>
                            <span>⚖️ Balanced</span>
                            <span>🎯 Highest Quality</span>
                        </div>
                    </div>

                    <div class="option-group">
                        <label class="option-label">
                            <i class="fas fa-cube"></i> Output Options
                        </label>
                        <div style="display: grid; gap: 8px;">
                            <label style="display: flex; align-items: center; gap: 8px; font-weight: 500; cursor: pointer;">
                                <input type="checkbox" id="3d-visualization" checked style="transform: scale(1.2);">
                                <span>3D Visualization</span>
                            </label>
                            <label style="display: flex; align-items: center; gap: 8px; font-weight: 500; cursor: pointer;">
                                <input type="checkbox" id="volume-analysis" checked style="transform: scale(1.2);">
                                <span>Volume Analysis</span>
                            </label>
                            <label style="display: flex; align-items: center; gap: 8px; font-weight: 500; cursor: pointer;">
                                <input type="checkbox" id="performance-comparison" checked style="transform: scale(1.2);">
                                <span>Performance Comparison</span>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div style="display: grid; gap: 10px;">
                    <button class="btn btn-primary" id="process-btn" onclick="startProcessing()" disabled>
                        <i class="fas fa-play"></i> Start Processing
                    </button>
                    <button class="btn btn-secondary" id="compare-btn" onclick="compareResults()" disabled>
                        <i class="fas fa-chart-bar"></i> Compare Results
                    </button>
                </div>
            </div>

            <!-- Processing Panel -->
            <div class="processing-panel">
                <div class="processing-header">
                    <div class="processing-icon">
                        <i class="fas fa-microchip"></i>
                    </div>
                    <div>
                        <div class="processing-title">Real-time Processing</div>
                        <div style="color: var(--text-secondary); font-size: 0.9rem;">Advanced 3D segmentation and analysis</div>
                    </div>
                </div>

                <!-- Image Display -->
                <div class="image-display">
                    <div class="image-container">
                        <div class="image-placeholder">
                            <i class="fas fa-image"></i>
                        </div>
                        <div class="image-label">Original MRI</div>
                        <div class="image-description">Uploaded brain scan</div>
                    </div>
                    <div class="image-container">
                        <div class="image-placeholder">
                            <i class="fas fa-brain"></i>
                        </div>
                        <div class="image-label">Segmented Result</div>
                        <div class="image-description">Processed output</div>
                    </div>
                </div>

                <!-- Progress Bar -->
                <div class="progress-container" id="progress-container" style="display: none;">
                    <div class="progress-label">
                        <span class="progress-text" id="progress-text">Initializing...</span>
                        <span class="progress-percentage" id="progress-percentage">0%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progress-fill"></div>
                    </div>
                </div>

                <!-- Status Messages -->
                <div id="status-messages"></div>

                <!-- Processing Steps -->
                <div id="processing-steps" style="display: none;">
                    <h4 style="margin-bottom: 15px; color: var(--text-primary);">
                        <i class="fas fa-list-ol"></i> Processing Steps
                    </h4>
                    <div style="display: grid; gap: 10px;">
                        <div class="processing-step" data-step="1">
                            <i class="fas fa-circle-notch fa-spin" style="color: #3b82f6;"></i>
                            <span>Loading and preprocessing MRI data...</span>
                        </div>
                        <div class="processing-step" data-step="2">
                            <i class="fas fa-circle" style="color: #e2e8f0;"></i>
                            <span>Applying machine learning segmentation...</span>
                        </div>
                        <div class="processing-step" data-step="3">
                            <i class="fas fa-circle" style="color: #e2e8f0;"></i>
                            <span>Running deep learning analysis...</span>
                        </div>
                        <div class="processing-step" data-step="4">
                            <i class="fas fa-circle" style="color: #e2e8f0;"></i>
                            <span>Generating 3D visualization...</span>
                        </div>
                        <div class="processing-step" data-step="5">
                            <i class="fas fa-circle" style="color: #e2e8f0;"></i>
                            <span>Computing performance metrics...</span>
                        </div>
                    </div>
                </div>

                <!-- 3D Visualization Container -->
                <div id="3d-container" style="display: none; margin-top: 25px;">
                    <h4 style="margin-bottom: 15px; color: var(--text-primary);">
                        <i class="fas fa-cube"></i> 3D Brain Visualization
                    </h4>
                    <div id="3d-viewer" style="height: 300px; background: #f8fafc; border-radius: 8px; border: 1px solid #e2e8f0;"></div>
                </div>
            </div>

            <!-- Results Panel -->
            <div class="results-panel">
                <div class="results-header">
                    <i class="fas fa-chart-line"></i>
                    <div class="results-title">Analysis Results</div>
                </div>

                <!-- Performance Metrics -->
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-value" id="ml-accuracy">--</div>
                        <div class="metric-label">ML Accuracy</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="dl-accuracy">--</div>
                        <div class="metric-label">DL Accuracy</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="ml-time">--</div>
                        <div class="metric-label">ML Time (s)</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="dl-time">--</div>
                        <div class="metric-label">DL Time (s)</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="tumor-volume">--</div>
                        <div class="metric-label">Tumor Volume</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" id="brain-volume">--</div>
                        <div class="metric-label">Brain Volume</div>
                    </div>
                </div>

                <!-- Best Method Indicator -->
                <div id="best-method" style="display: none; margin: 20px 0;">
                    <div style="background: linear-gradient(135deg, #dcfce7, #bbf7d0); border: 1px solid #22c55e; border-radius: 8px; padding: 15px; text-align: center;">
                        <div style="color: #166534; font-weight: 600; margin-bottom: 5px;">
                            <i class="fas fa-trophy"></i> Best Performance
                        </div>
                        <div id="best-method-name" style="color: #166534; font-weight: 700; font-size: 1.1rem;">
                            Deep Learning (U-Net)
                        </div>
                    </div>
                </div>

                <!-- Comparison Chart -->
                <div class="comparison-section">
                    <div class="comparison-title">
                        <i class="fas fa-chart-bar"></i> Performance Comparison
                    </div>
                    <div class="comparison-chart" id="comparison-chart"></div>
                </div>

                <!-- Download Results -->
                <div style="margin-top: 25px;">
                    <button class="btn btn-secondary" id="download-btn" onclick="downloadResults()" disabled>
                        <i class="fas fa-download"></i> Download Results
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // MRI Processing GUI Application
        class MRIProcessingGUI {
            constructor() {
                this.uploadedFile = null;
                this.processingActive = false;
                this.selectedMethods = ['ml', 'dl'];
                this.results = {
                    ml: { accuracy: 0, time: 0, volume: 0 },
                    dl: { accuracy: 0, time: 0, volume: 0 }
                };
                this.scene3D = null;
                this.init();
            }

            init() {
                console.log('🚀 Initializing MRI Processing GUI...');
                this.setupFileUpload();
                this.setupMethodSelection();
                this.setupAnimations();
                this.initializeCharts();
                console.log('✅ GUI ready!');
            }

            setupFileUpload() {
                const uploadArea = document.getElementById('upload-area');
                const fileInput = document.getElementById('file-input');

                // Click to upload
                uploadArea.addEventListener('click', () => {
                    fileInput.click();
                });

                // File selection
                fileInput.addEventListener('change', (e) => {
                    this.handleFileSelection(e.target.files);
                });

                // Drag and drop
                uploadArea.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    uploadArea.classList.add('dragover');
                });

                uploadArea.addEventListener('dragleave', () => {
                    uploadArea.classList.remove('dragover');
                });

                uploadArea.addEventListener('drop', (e) => {
                    e.preventDefault();
                    uploadArea.classList.remove('dragover');
                    this.handleFileSelection(e.dataTransfer.files);
                });
            }

            handleFileSelection(files) {
                if (files.length === 0) return;

                const file = files[0];
                const validExtensions = ['.dcm', '.nii', '.nii.gz', '.img', '.hdr'];
                const isValid = validExtensions.some(ext => file.name.toLowerCase().endsWith(ext));

                if (!isValid) {
                    this.showStatusMessage('Please select a valid MRI file (.dcm, .nii, .img, .hdr)', 'error');
                    return;
                }

                this.uploadedFile = file;
                this.displayUploadedFile(file);
                this.enableProcessing();
                this.showStatusMessage(`File "${file.name}" uploaded successfully!`, 'success');
            }

            displayUploadedFile(file) {
                const uploadedFileDiv = document.getElementById('uploaded-file');
                const fileName = document.getElementById('file-name');
                const fileSize = document.getElementById('file-size');

                fileName.textContent = file.name;
                fileSize.textContent = this.formatFileSize(file.size);
                uploadedFileDiv.style.display = 'block';

                // Animate the file display
                if (window.gsap) {
                    gsap.fromTo(uploadedFileDiv,
                        { opacity: 0, y: 20 },
                        { duration: 0.5, opacity: 1, y: 0, ease: "back.out(1.7)" }
                    );
                }

                // Simulate image preview
                this.displayImagePreview(file);
            }

            displayImagePreview(file) {
                const originalContainer = document.querySelector('.image-container:first-child');
                const placeholder = originalContainer.querySelector('.image-placeholder');

                // Create a simulated MRI image preview
                const canvas = document.createElement('canvas');
                canvas.width = 200;
                canvas.height = 200;
                canvas.style.maxWidth = '100%';
                canvas.style.maxHeight = '180px';
                canvas.style.borderRadius = '8px';
                canvas.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.1)';

                const ctx = canvas.getContext('2d');
                this.generateMRIPreview(ctx, canvas.width, canvas.height);

                placeholder.innerHTML = '';
                placeholder.appendChild(canvas);
            }

            generateMRIPreview(ctx, width, height) {
                // Generate a realistic-looking MRI brain slice
                const centerX = width / 2;
                const centerY = height / 2;

                // Background
                ctx.fillStyle = '#000000';
                ctx.fillRect(0, 0, width, height);

                // Brain outline
                ctx.fillStyle = '#404040';
                ctx.beginPath();
                ctx.ellipse(centerX, centerY, width * 0.35, height * 0.4, 0, 0, 2 * Math.PI);
                ctx.fill();

                // Brain tissue
                ctx.fillStyle = '#808080';
                ctx.beginPath();
                ctx.ellipse(centerX, centerY, width * 0.3, height * 0.35, 0, 0, 2 * Math.PI);
                ctx.fill();

                // Ventricles
                ctx.fillStyle = '#202020';
                ctx.beginPath();
                ctx.ellipse(centerX - 20, centerY - 10, 15, 25, 0, 0, 2 * Math.PI);
                ctx.fill();
                ctx.beginPath();
                ctx.ellipse(centerX + 20, centerY - 10, 15, 25, 0, 0, 2 * Math.PI);
                ctx.fill();

                // Add some noise for realism
                for (let i = 0; i < 1000; i++) {
                    const x = Math.random() * width;
                    const y = Math.random() * height;
                    const brightness = Math.random() * 50;
                    ctx.fillStyle = `rgba(${brightness}, ${brightness}, ${brightness}, 0.3)`;
                    ctx.fillRect(x, y, 1, 1);
                }
            }

            formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            enableProcessing() {
                const processBtn = document.getElementById('process-btn');
                processBtn.disabled = false;

                if (window.gsap) {
                    gsap.to(processBtn, { duration: 0.3, scale: 1.05 });
                    gsap.to(processBtn, { duration: 0.3, scale: 1, delay: 0.1 });
                }
            }

            setupMethodSelection() {
                const methodOptions = document.querySelectorAll('.method-option');
                methodOptions.forEach(option => {
                    option.addEventListener('click', () => {
                        option.classList.toggle('selected');
                        this.updateSelectedMethods();
                    });
                });
            }

            updateSelectedMethods() {
                const selectedOptions = document.querySelectorAll('.method-option.selected');
                this.selectedMethods = Array.from(selectedOptions).map(option => option.dataset.method);

                if (this.selectedMethods.length === 0) {
                    this.showStatusMessage('Please select at least one processing method', 'warning');
                }
            }

            setupAnimations() {
                if (!window.gsap) return;

                // Animate upload panel
                gsap.fromTo('.upload-panel',
                    { opacity: 0, x: -50 },
                    { duration: 0.8, opacity: 1, x: 0, ease: "power2.out" }
                );

                // Animate processing panel
                gsap.fromTo('.processing-panel',
                    { opacity: 0, y: 30 },
                    { duration: 0.8, opacity: 1, y: 0, ease: "power2.out", delay: 0.2 }
                );

                // Animate results panel
                gsap.fromTo('.results-panel',
                    { opacity: 0, x: 50 },
                    { duration: 0.8, opacity: 1, x: 0, ease: "power2.out", delay: 0.4 }
                );
            }

            initializeCharts() {
                // Initialize empty comparison chart
                const container = document.getElementById('comparison-chart');
                if (!container || !window.Plotly) return;

                const data = [{
                    x: ['Machine Learning', 'Deep Learning'],
                    y: [0, 0],
                    type: 'bar',
                    marker: {
                        color: ['#f093fb', '#4facfe']
                    },
                    text: ['0%', '0%'],
                    textposition: 'auto'
                }];

                const layout = {
                    title: {
                        text: 'Accuracy Comparison',
                        font: { size: 14, color: '#1e293b' }
                    },
                    xaxis: { title: 'Methods' },
                    yaxis: { title: 'Accuracy (%)', range: [0, 100] },
                    margin: { t: 40, r: 20, b: 40, l: 40 },
                    paper_bgcolor: 'transparent',
                    plot_bgcolor: 'white',
                    font: { family: 'Inter, sans-serif' }
                };

                const config = {
                    responsive: true,
                    displayModeBar: false
                };

                Plotly.newPlot(container, data, layout, config);
            }

            showStatusMessage(message, type = 'info') {
                const statusContainer = document.getElementById('status-messages');
                const messageDiv = document.createElement('div');
                messageDiv.className = `status-message ${type}`;

                const icons = {
                    'success': 'fas fa-check-circle',
                    'warning': 'fas fa-exclamation-triangle',
                    'error': 'fas fa-times-circle',
                    'info': 'fas fa-info-circle'
                };

                messageDiv.innerHTML = `
                    <i class="${icons[type]}"></i>
                    <span>${message}</span>
                `;

                statusContainer.appendChild(messageDiv);

                // Animate in
                if (window.gsap) {
                    gsap.fromTo(messageDiv,
                        { opacity: 0, y: 20 },
                        { duration: 0.5, opacity: 1, y: 0, ease: "back.out(1.7)" }
                    );
                }

                // Auto remove after 5 seconds
                setTimeout(() => {
                    if (window.gsap) {
                        gsap.to(messageDiv, {
                            duration: 0.3,
                            opacity: 0,
                            y: -20,
                            onComplete: () => messageDiv.remove()
                        });
                    } else {
                        messageDiv.remove();
                    }
                }, 5000);
            }

            async startProcessing() {
                if (!this.uploadedFile || this.processingActive) return;

                this.processingActive = true;
                this.showProcessingUI();

                try {
                    // Step 1: Preprocessing
                    await this.simulateProcessingStep(1, 'Loading and preprocessing MRI data...', 2000);

                    // Step 2: Machine Learning (if selected)
                    if (this.selectedMethods.includes('ml')) {
                        await this.simulateProcessingStep(2, 'Applying machine learning segmentation...', 3000);
                        this.results.ml = {
                            accuracy: 85 + Math.random() * 10,
                            time: 2.5 + Math.random() * 2,
                            volume: 45.2 + Math.random() * 10
                        };
                    }

                    // Step 3: Deep Learning (if selected)
                    if (this.selectedMethods.includes('dl')) {
                        await this.simulateProcessingStep(3, 'Running deep learning analysis...', 4000);
                        this.results.dl = {
                            accuracy: 92 + Math.random() * 6,
                            time: 0.8 + Math.random() * 0.5,
                            volume: 47.8 + Math.random() * 8
                        };
                    }

                    // Step 4: 3D Visualization
                    await this.simulateProcessingStep(4, 'Generating 3D visualization...', 2500);
                    this.generate3DVisualization();

                    // Step 5: Performance Analysis
                    await this.simulateProcessingStep(5, 'Computing performance metrics...', 1500);
                    this.updateResults();

                    this.showStatusMessage('Processing completed successfully!', 'success');
                    this.enableResultsDownload();

                } catch (error) {
                    this.showStatusMessage('Processing failed: ' + error.message, 'error');
                } finally {
                    this.processingActive = false;
                }
            }

            showProcessingUI() {
                const progressContainer = document.getElementById('progress-container');
                const processingSteps = document.getElementById('processing-steps');

                progressContainer.style.display = 'block';
                processingSteps.style.display = 'block';

                if (window.gsap) {
                    gsap.fromTo([progressContainer, processingSteps],
                        { opacity: 0, y: 20 },
                        { duration: 0.5, opacity: 1, y: 0, stagger: 0.1 }
                    );
                }
            }

            async simulateProcessingStep(stepNumber, message, duration) {
                return new Promise((resolve) => {
                    // Update progress text
                    document.getElementById('progress-text').textContent = message;

                    // Update step indicators
                    const currentStep = document.querySelector(`[data-step="${stepNumber}"]`);
                    const currentIcon = currentStep.querySelector('i');

                    // Set current step as active
                    currentIcon.className = 'fas fa-circle-notch fa-spin';
                    currentIcon.style.color = '#3b82f6';

                    // Animate progress bar
                    const progress = (stepNumber / 5) * 100;
                    const progressFill = document.getElementById('progress-fill');
                    const progressPercentage = document.getElementById('progress-percentage');

                    if (window.gsap) {
                        gsap.to(progressFill, { duration: duration / 1000, width: `${progress}%` });
                        gsap.to({ value: 0 }, {
                            duration: duration / 1000,
                            value: progress,
                            onUpdate: function() {
                                progressPercentage.textContent = Math.round(this.targets()[0].value) + '%';
                            }
                        });
                    } else {
                        progressFill.style.width = `${progress}%`;
                        progressPercentage.textContent = progress + '%';
                    }

                    setTimeout(() => {
                        // Mark step as completed
                        currentIcon.className = 'fas fa-check-circle';
                        currentIcon.style.color = '#22c55e';
                        resolve();
                    }, duration);
                });
            }

            generate3DVisualization() {
                const container3D = document.getElementById('3d-container');
                const viewer3D = document.getElementById('3d-viewer');

                container3D.style.display = 'block';

                if (window.gsap) {
                    gsap.fromTo(container3D,
                        { opacity: 0, y: 30 },
                        { duration: 0.8, opacity: 1, y: 0, ease: "power2.out" }
                    );
                }

                // Create a simple 3D brain visualization using Three.js
                if (window.THREE) {
                    this.create3DBrainVisualization(viewer3D);
                } else {
                    // Fallback: Create a canvas-based visualization
                    this.createCanvasBrainVisualization(viewer3D);
                }
            }

            create3DBrainVisualization(container) {
                const scene = new THREE.Scene();
                const camera = new THREE.PerspectiveCamera(75, container.offsetWidth / container.offsetHeight, 0.1, 1000);
                const renderer = new THREE.WebGLRenderer({ antialias: true });

                renderer.setSize(container.offsetWidth, container.offsetHeight);
                renderer.setClearColor(0xf8fafc);
                container.appendChild(renderer.domElement);

                // Create brain geometry
                const brainGeometry = new THREE.SphereGeometry(1, 32, 32);
                const brainMaterial = new THREE.MeshPhongMaterial({
                    color: 0x888888,
                    transparent: true,
                    opacity: 0.8
                });
                const brain = new THREE.Mesh(brainGeometry, brainMaterial);
                scene.add(brain);

                // Add tumor (segmented region)
                const tumorGeometry = new THREE.SphereGeometry(0.3, 16, 16);
                const tumorMaterial = new THREE.MeshPhongMaterial({ color: 0xff4444 });
                const tumor = new THREE.Mesh(tumorGeometry, tumorMaterial);
                tumor.position.set(0.4, 0.2, 0.3);
                scene.add(tumor);

                // Add lighting
                const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
                scene.add(ambientLight);
                const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
                directionalLight.position.set(1, 1, 1);
                scene.add(directionalLight);

                camera.position.z = 3;

                // Animation loop
                const animate = () => {
                    requestAnimationFrame(animate);
                    brain.rotation.y += 0.01;
                    tumor.rotation.y += 0.01;
                    renderer.render(scene, camera);
                };
                animate();

                this.scene3D = { scene, camera, renderer };
            }

            createCanvasBrainVisualization(container) {
                const canvas = document.createElement('canvas');
                canvas.width = container.offsetWidth;
                canvas.height = container.offsetHeight;
                canvas.style.width = '100%';
                canvas.style.height = '100%';
                container.appendChild(canvas);

                const ctx = canvas.getContext('2d');

                // Simple 2D brain visualization
                const centerX = canvas.width / 2;
                const centerY = canvas.height / 2;

                // Brain outline
                ctx.fillStyle = '#888888';
                ctx.beginPath();
                ctx.ellipse(centerX, centerY, 80, 100, 0, 0, 2 * Math.PI);
                ctx.fill();

                // Segmented regions
                ctx.fillStyle = '#ff4444';
                ctx.beginPath();
                ctx.ellipse(centerX + 30, centerY - 20, 20, 25, 0, 0, 2 * Math.PI);
                ctx.fill();

                ctx.fillStyle = '#44ff44';
                ctx.beginPath();
                ctx.ellipse(centerX - 25, centerY + 15, 15, 20, 0, 0, 2 * Math.PI);
                ctx.fill();

                // Add labels
                ctx.fillStyle = '#1e293b';
                ctx.font = '12px Inter';
                ctx.fillText('Tumor', centerX + 10, centerY - 40);
                ctx.fillText('Healthy Tissue', centerX - 60, centerY + 40);
            }

            updateResults() {
                // Update metric cards
                if (this.selectedMethods.includes('ml')) {
                    document.getElementById('ml-accuracy').textContent = this.results.ml.accuracy.toFixed(1) + '%';
                    document.getElementById('ml-time').textContent = this.results.ml.time.toFixed(1);
                }

                if (this.selectedMethods.includes('dl')) {
                    document.getElementById('dl-accuracy').textContent = this.results.dl.accuracy.toFixed(1) + '%';
                    document.getElementById('dl-time').textContent = this.results.dl.time.toFixed(1);
                }

                // Update volume metrics
                const avgVolume = (this.results.ml.volume + this.results.dl.volume) / 2;
                document.getElementById('tumor-volume').textContent = avgVolume.toFixed(1) + ' cm³';
                document.getElementById('brain-volume').textContent = (1200 + Math.random() * 200).toFixed(0) + ' cm³';

                // Determine best method
                this.determineBestMethod();

                // Update comparison chart
                this.updateComparisonChart();

                // Animate metric cards
                if (window.gsap) {
                    const metricCards = document.querySelectorAll('.metric-card');
                    gsap.fromTo(metricCards,
                        { scale: 0.8, opacity: 0 },
                        {
                            duration: 0.6,
                            scale: 1,
                            opacity: 1,
                            stagger: 0.1,
                            ease: "back.out(1.7)"
                        }
                    );
                }
            }

            determineBestMethod() {
                const bestMethodDiv = document.getElementById('best-method');
                const bestMethodName = document.getElementById('best-method-name');

                let bestMethod = '';
                let bestScore = 0;

                if (this.selectedMethods.includes('ml') && this.results.ml.accuracy > bestScore) {
                    bestScore = this.results.ml.accuracy;
                    bestMethod = 'Machine Learning (Random Forest)';
                }

                if (this.selectedMethods.includes('dl') && this.results.dl.accuracy > bestScore) {
                    bestScore = this.results.dl.accuracy;
                    bestMethod = 'Deep Learning (U-Net)';
                }

                if (bestMethod) {
                    bestMethodName.textContent = bestMethod;
                    bestMethodDiv.style.display = 'block';

                    if (window.gsap) {
                        gsap.fromTo(bestMethodDiv,
                            { opacity: 0, scale: 0.9 },
                            { duration: 0.8, opacity: 1, scale: 1, ease: "back.out(1.7)" }
                        );
                    }
                }
            }

            updateComparisonChart() {
                const container = document.getElementById('comparison-chart');
                if (!container || !window.Plotly) return;

                const methods = [];
                const accuracies = [];
                const colors = [];

                if (this.selectedMethods.includes('ml')) {
                    methods.push('Machine Learning');
                    accuracies.push(this.results.ml.accuracy);
                    colors.push('#f093fb');
                }

                if (this.selectedMethods.includes('dl')) {
                    methods.push('Deep Learning');
                    accuracies.push(this.results.dl.accuracy);
                    colors.push('#4facfe');
                }

                const update = {
                    x: [methods],
                    y: [accuracies],
                    text: [accuracies.map(a => a.toFixed(1) + '%')],
                    'marker.color': [colors]
                };

                Plotly.restyle(container, update, 0);
            }

            enableResultsDownload() {
                const downloadBtn = document.getElementById('download-btn');
                const compareBtn = document.getElementById('compare-btn');

                downloadBtn.disabled = false;
                compareBtn.disabled = false;

                if (window.gsap) {
                    gsap.to([downloadBtn, compareBtn], {
                        duration: 0.3,
                        scale: 1.05,
                        onComplete: () => {
                            gsap.to([downloadBtn, compareBtn], { duration: 0.3, scale: 1 });
                        }
                    });
                }
            }
        }

        // Global functions
        function startProcessing() {
            window.mriGUI.startProcessing();
        }

        function compareResults() {
            window.mriGUI.showStatusMessage('📊 Generating detailed comparison report...', 'info');

            setTimeout(() => {
                window.mriGUI.showStatusMessage('✅ Comparison report ready!', 'success');

                // Animate comparison chart
                const chart = document.getElementById('comparison-chart');
                if (window.gsap) {
                    gsap.fromTo(chart,
                        { scale: 0.9, opacity: 0.7 },
                        { duration: 0.8, scale: 1, opacity: 1, ease: "back.out(1.7)" }
                    );
                }
            }, 2000);
        }

        function downloadResults() {
            window.mriGUI.showStatusMessage('📥 Preparing download package...', 'info');

            // Simulate download preparation
            setTimeout(() => {
                const results = {
                    filename: window.mriGUI.uploadedFile?.name || 'mri_scan.nii',
                    timestamp: new Date().toISOString(),
                    methods: window.mriGUI.selectedMethods,
                    results: window.mriGUI.results,
                    summary: {
                        bestMethod: document.getElementById('best-method-name').textContent,
                        tumorVolume: document.getElementById('tumor-volume').textContent,
                        brainVolume: document.getElementById('brain-volume').textContent
                    }
                };

                // Create and download JSON file
                const blob = new Blob([JSON.stringify(results, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'mri_analysis_results.json';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                window.mriGUI.showStatusMessage('✅ Results downloaded successfully!', 'success');
            }, 1500);
        }

        function removeFile() {
            const uploadedFileDiv = document.getElementById('uploaded-file');
            const processBtn = document.getElementById('process-btn');
            const compareBtn = document.getElementById('compare-btn');
            const downloadBtn = document.getElementById('download-btn');

            uploadedFileDiv.style.display = 'none';
            processBtn.disabled = true;
            compareBtn.disabled = true;
            downloadBtn.disabled = true;

            window.mriGUI.uploadedFile = null;
            window.mriGUI.showStatusMessage('File removed', 'info');

            // Reset image display
            const originalContainer = document.querySelector('.image-container:first-child .image-placeholder');
            originalContainer.innerHTML = '<i class="fas fa-image"></i>';
        }

        // Initialize application
        document.addEventListener('DOMContentLoaded', () => {
            window.mriGUI = new MRIProcessingGUI();
            console.log('🧠💻 MRI Processing GUI loaded successfully!');
        });
    </script>
</body>
</html>


import React from 'react';
import SectionCard from '../components/SectionCard';
import CodeBlock from '../components/CodeBlock';

const DicomProcessing: React.FC = () => {
  return (
    <div>
      <h2 className="text-3xl font-extrabold text-slate-900 dark:text-white mb-6">🏥 معالجة صور DICOM</h2>
      
      <SectionCard title="مقدمة عن معيار DICOM">
        <p>DICOM (Digital Imaging and Communications in Medicine) هو المعيار العالمي لنقل وتخزين واسترجاع الصور الطبية والمعلومات المتعلقة بها. كل ملف DICOM يحتوي على بيانات الصورة نفسها بالإضافة إلى "بيانات وصفية" (metadata) غنية مثل معلومات المريض، نوع الفحص، إعدادات الجهاز، وغيرها.</p>
      </SectionCard>

      <SectionCard title="قراءة وتحليل ملفات DICOM">
        <p>يوفر الماتلاب دوال مخصصة للتعامل مع ملفات DICOM بسهولة. الدالة `dicominfo` تقرأ البيانات الوصفية، بينما `dicomread` تقرأ بيانات البكسلات الفعلية للصورة.</p>
        <CodeBlock code={`% تحديد مسار الملف\nfilePath = 'brain_mri.dcm';\n\n% قراءة البيانات الوصفية (Metadata)\ninfo = dicominfo(filePath);\ndisp(info); % عرض معلومات المريض ونوع الجهاز وغيرها\n\n% قراءة بيانات الصورة\nimage_data = dicomread(filePath);\n\n% عرض الصورة\nfigure;\nimshow(image_data, []); % استخدام [] لضبط تدرج الألوان تلقائيًا`} />
      </SectionCard>
      
      <SectionCard title="تحسين جودة الصور الطبية">
        <p>الصور الطبية قد تحتاج إلى تحسين لزيادة وضوحها وتسهيل التشخيص. يمكن استخدام دوال مثل `imadjust` لتعديل التباين أو المرشحات (filters) لتقليل الضوضاء.</p>
        <CodeBlock code={`% تحسين تباين الصورة\nadjusted_image = imadjust(image_data);\n\n% استخدام مرشح متوسط (Median Filter) لتقليل الضوضاء\nfiltered_image = medfilt2(image_data, [3 3]);\n\n% عرض الصور للمقارنة\nfigure;\nsubplot(1, 3, 1), imshow(image_data, []), title('الصورة الأصلية');\nsubplot(1, 3, 2), imshow(adjusted_image, []), title('صورة مُحسنة التباين');\nsubplot(1, 3, 3), imshow(filtered_image, []), title('صورة مُرشحة');`} />
      </SectionCard>

      <SectionCard title="التجزئة والتحليل المتقدم">
        <p>التجزئة (Segmentation) هي عملية تقسيم الصورة إلى مناطق أو كائنات ذات معنى، مثل فصل ورم عن الأنسجة السليمة. يمكن استخدام تقنيات مثل العتبة (Thresholding) لتحقيق ذلك.</p>
        <CodeBlock code={`% تحويل الصورة إلى تدرج رمادي إذا لزم الأمر\nif size(image_data, 3) > 1\n    gray_image = rgb2gray(image_data);\nelse\n    gray_image = image_data;\nend\n\n% تطبيق عتبة بسيطة لفصل المناطق الساطعة\nthreshold = 150; % هذه القيمة تعتمد على الصورة\nbinary_mask = gray_image > threshold;\n\n% عرض القناع الثنائي\nfigure;\nimshow(binary_mask);\ntitle('نتيجة التجزئة باستخدام العتبة');`} />
      </SectionCard>
    </div>
  );
};

export default DicomProcessing;

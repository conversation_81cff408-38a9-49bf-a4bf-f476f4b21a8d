import React from 'react';
import { SectionId, Section } from '../types';

interface SidebarProps {
  activeSection: SectionId;
  setActiveSection: (section: SectionId) => void;
}

const sections: Section[] = [
  { id: SectionId.Introduction, title: '🏠 المقدمة' },
  { id: SectionId.MatlabBasics, title: '🔧 أساسيات الماتلاب' },
  { id: SectionId.DicomProcessing, title: '🏥 معالجة صور DICOM' },
  { id: SectionId.AdvancedExamples, title: '💡 أمثلة تطبيقية' },
  { id: SectionId.DeepLearningSegmentation, title: '🧠 تجزئة أورام الدماغ' }
];

const Sidebar: React.FC<SidebarProps> = ({ activeSection, setActiveSection }) => {
  return (
    <aside className="w-full md:w-64 bg-white dark:bg-slate-800 p-4 md:p-6 shadow-lg border-s-0 md:border-s border-slate-200 dark:border-slate-700">
      <nav>
        <ul>
          {sections.map((section) => (
            <li key={section.id} className="mb-2">
              <button
                onClick={() => setActiveSection(section.id)}
                className={`w-full text-right px-4 py-2.5 rounded-lg transition-all duration-200 text-md font-semibold ${
                  activeSection === section.id
                    ? 'bg-sky-500 text-white shadow-md'
                    : 'text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700'
                }`}
              >
                {section.title}
              </button>
            </li>
          ))}
        </ul>
      </nav>
    </aside>
  );
};

export default Sidebar;
// DICOM Image Processing Module

class DICOMProcessor {
    constructor() {
        this.currentImage = null;
        this.imageStack = [];
        this.currentSlice = 0;
        this.canvas = null;
        this.ctx = null;
        this.imageData = null;
        this.originalImageData = null;
        
        // Image processing parameters
        this.brightness = 0;
        this.contrast = 100;
        this.windowLevel = 2048;
        this.windowWidth = 1024;
        
        this.init();
    }
    
    init() {
        this.canvas = document.getElementById('dicom-canvas');
        if (this.canvas) {
            this.ctx = this.canvas.getContext('2d');
            this.canvas.width = 512;
            this.canvas.height = 512;
        }
        
        this.setupEventListeners();
        this.generateSampleDICOM();
        console.log('DICOM Processor initialized');
    }
    
    setupEventListeners() {
        // File upload
        const fileInput = document.getElementById('dicom-file-input');
        if (fileInput) {
            fileInput.addEventListener('change', (e) => {
                this.handleFileUpload(e.target.files);
            });
        }
        
        // Image processing controls
        this.setupImageControls();
        
        // Mouse interactions for windowing
        if (this.canvas) {
            this.setupMouseControls();
        }
    }
    
    setupImageControls() {
        const controls = {
            'brightness-slider': (value) => this.setBrightness(value),
            'contrast-slider': (value) => this.setContrast(value),
            'window-level-slider': (value) => this.setWindowLevel(value),
            'window-width-slider': (value) => this.setWindowWidth(value),
            'slice-slider': (value) => this.setSlice(value)
        };
        
        Object.entries(controls).forEach(([id, handler]) => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('input', (e) => {
                    handler(parseInt(e.target.value));
                    this.updateDisplay();
                });
            }
        });
    }
    
    setupMouseControls() {
        let isMouseDown = false;
        let lastMouseX = 0;
        let lastMouseY = 0;
        
        this.canvas.addEventListener('mousedown', (e) => {
            isMouseDown = true;
            lastMouseX = e.clientX;
            lastMouseY = e.clientY;
            this.canvas.style.cursor = 'grabbing';
        });
        
        this.canvas.addEventListener('mousemove', (e) => {
            if (!isMouseDown) return;
            
            const deltaX = e.clientX - lastMouseX;
            const deltaY = e.clientY - lastMouseY;
            
            // Adjust window level and width based on mouse movement
            this.windowLevel += deltaX * 2;
            this.windowWidth += deltaY * 2;
            
            // Clamp values
            this.windowLevel = Math.max(0, Math.min(4096, this.windowLevel));
            this.windowWidth = Math.max(1, Math.min(4096, this.windowWidth));
            
            // Update sliders
            const levelSlider = document.getElementById('window-level-slider');
            const widthSlider = document.getElementById('window-width-slider');
            
            if (levelSlider) levelSlider.value = this.windowLevel;
            if (widthSlider) widthSlider.value = this.windowWidth;
            
            this.updateDisplay();
            
            lastMouseX = e.clientX;
            lastMouseY = e.clientY;
        });
        
        this.canvas.addEventListener('mouseup', () => {
            isMouseDown = false;
            this.canvas.style.cursor = 'grab';
        });
        
        this.canvas.addEventListener('mouseleave', () => {
            isMouseDown = false;
            this.canvas.style.cursor = 'default';
        });
        
        // Zoom with mouse wheel
        this.canvas.addEventListener('wheel', (e) => {
            e.preventDefault();
            const delta = e.deltaY > 0 ? -10 : 10;
            this.windowWidth += delta;
            this.windowWidth = Math.max(1, Math.min(4096, this.windowWidth));
            
            const widthSlider = document.getElementById('window-width-slider');
            if (widthSlider) widthSlider.value = this.windowWidth;
            
            this.updateDisplay();
        });
    }
    
    handleFileUpload(files) {
        if (files.length === 0) return;
        
        Array.from(files).forEach((file, index) => {
            const reader = new FileReader();
            
            reader.onload = (e) => {
                try {
                    // In a real implementation, you would use a DICOM parser like cornerstone.js
                    // For this demo, we'll simulate DICOM processing
                    this.processDICOMFile(e.target.result, file.name, index);
                } catch (error) {
                    console.error('Error processing DICOM file:', error);
                    this.showError(`Failed to process ${file.name}`);
                }
            };
            
            reader.readAsArrayBuffer(file);
        });
    }
    
    processDICOMFile(arrayBuffer, filename, index) {
        // Simulate DICOM parsing
        const simulatedDICOM = this.simulateDICOMParsing(arrayBuffer, filename);
        
        if (index === 0) {
            this.currentImage = simulatedDICOM;
            this.originalImageData = simulatedDICOM.imageData;
            this.displayImage(simulatedDICOM);
        }
        
        this.imageStack.push(simulatedDICOM);
        this.updateSliceControls();
        
        console.log(`Processed DICOM file: ${filename}`);
    }
    
    simulateDICOMParsing(arrayBuffer, filename) {
        // Generate realistic medical image data
        const width = 512;
        const height = 512;
        const imageData = new Uint16Array(width * height);
        
        // Create brain-like structure
        const centerX = width / 2;
        const centerY = height / 2;
        
        for (let y = 0; y < height; y++) {
            for (let x = 0; x < width; x++) {
                const index = y * width + x;
                const distance = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);
                
                let intensity = 0;
                
                // Brain tissue
                if (distance < 200) {
                    intensity = 1000 + Math.sin(distance * 0.05) * 500;
                    
                    // Add anatomical structures
                    if (distance < 50) {
                        // Central structures
                        intensity += 800;
                    } else if (distance > 150) {
                        // Skull
                        intensity = 3000 + Math.random() * 500;
                    }
                    
                    // Add some tumor-like regions
                    const tumorX = centerX + 50;
                    const tumorY = centerY - 30;
                    const tumorDistance = Math.sqrt((x - tumorX) ** 2 + (y - tumorY) ** 2);
                    
                    if (tumorDistance < 25) {
                        intensity += 1200 + Math.sin(tumorDistance * 0.3) * 300;
                    }
                    
                    // Add noise
                    intensity += (Math.random() - 0.5) * 200;
                }
                
                imageData[index] = Math.max(0, Math.min(4095, intensity));
            }
        }
        
        return {
            filename: filename,
            width: width,
            height: height,
            imageData: imageData,
            pixelSpacing: [0.5, 0.5], // mm
            sliceThickness: 1.0, // mm
            windowCenter: 1024,
            windowWidth: 2048,
            rescaleIntercept: 0,
            rescaleSlope: 1,
            patientInfo: {
                name: 'Sample Patient',
                id: 'SP001',
                age: '45Y',
                sex: 'M'
            },
            studyInfo: {
                date: new Date().toISOString().split('T')[0],
                description: 'Brain MRI T1',
                modality: 'MR'
            }
        };
    }
    
    displayImage(dicomImage) {
        if (!this.canvas || !this.ctx) return;
        
        this.canvas.width = dicomImage.width;
        this.canvas.height = dicomImage.height;
        
        // Convert 16-bit DICOM data to 8-bit for canvas display
        const displayData = this.applyWindowLevelWidth(dicomImage.imageData);
        
        // Create ImageData for canvas
        const canvasImageData = this.ctx.createImageData(dicomImage.width, dicomImage.height);
        const data = canvasImageData.data;
        
        for (let i = 0; i < displayData.length; i++) {
            const pixelValue = displayData[i];
            const index = i * 4;
            
            data[index] = pixelValue;     // Red
            data[index + 1] = pixelValue; // Green
            data[index + 2] = pixelValue; // Blue
            data[index + 3] = 255;        // Alpha
        }
        
        this.imageData = canvasImageData;
        this.ctx.putImageData(canvasImageData, 0, 0);
        
        // Update image info display
        this.updateImageInfo(dicomImage);
    }
    
    applyWindowLevelWidth(imageData) {
        const result = new Uint8Array(imageData.length);
        const minValue = this.windowLevel - this.windowWidth / 2;
        const maxValue = this.windowLevel + this.windowWidth / 2;
        
        for (let i = 0; i < imageData.length; i++) {
            let value = imageData[i];
            
            // Apply window/level
            if (value <= minValue) {
                value = 0;
            } else if (value >= maxValue) {
                value = 255;
            } else {
                value = ((value - minValue) / this.windowWidth) * 255;
            }
            
            result[i] = Math.round(value);
        }
        
        return result;
    }
    
    updateDisplay() {
        if (!this.currentImage) return;
        
        // Apply all image processing
        let processedData = new Uint16Array(this.currentImage.imageData);
        
        // Apply brightness and contrast
        processedData = this.applyBrightnessContrast(processedData);
        
        // Apply window/level
        const displayData = this.applyWindowLevelWidth(processedData);
        
        // Update canvas
        if (this.imageData) {
            const data = this.imageData.data;
            
            for (let i = 0; i < displayData.length; i++) {
                const pixelValue = displayData[i];
                const index = i * 4;
                
                data[index] = pixelValue;
                data[index + 1] = pixelValue;
                data[index + 2] = pixelValue;
                data[index + 3] = 255;
            }
            
            this.ctx.putImageData(this.imageData, 0, 0);
        }
    }
    
    applyBrightnessContrast(imageData) {
        const result = new Uint16Array(imageData.length);
        const contrastFactor = this.contrast / 100;
        
        for (let i = 0; i < imageData.length; i++) {
            let value = imageData[i];
            
            // Apply contrast
            value = value * contrastFactor;
            
            // Apply brightness
            value = value + this.brightness * 10;
            
            result[i] = Math.max(0, Math.min(4095, value));
        }
        
        return result;
    }
    
    setBrightness(value) {
        this.brightness = value;
    }
    
    setContrast(value) {
        this.contrast = value;
    }
    
    setWindowLevel(value) {
        this.windowLevel = value;
    }
    
    setWindowWidth(value) {
        this.windowWidth = value;
    }
    
    setSlice(sliceIndex) {
        if (sliceIndex >= 0 && sliceIndex < this.imageStack.length) {
            this.currentSlice = sliceIndex;
            this.currentImage = this.imageStack[sliceIndex];
            this.displayImage(this.currentImage);
            
            // Update slice display
            const currentSliceEl = document.getElementById('current-slice');
            if (currentSliceEl) {
                currentSliceEl.textContent = sliceIndex + 1;
            }
        }
    }
    
    updateSliceControls() {
        const sliceSlider = document.getElementById('slice-slider');
        const totalSlicesEl = document.getElementById('total-slices');
        
        if (sliceSlider) {
            sliceSlider.max = Math.max(1, this.imageStack.length);
            sliceSlider.value = this.currentSlice + 1;
        }
        
        if (totalSlicesEl) {
            totalSlicesEl.textContent = this.imageStack.length;
        }
    }
    
    updateImageInfo(dicomImage) {
        // Create or update image info panel
        let infoPanel = document.querySelector('.dicom-info-panel');
        
        if (!infoPanel) {
            infoPanel = document.createElement('div');
            infoPanel.className = 'dicom-info-panel';
            infoPanel.style.cssText = `
                position: absolute;
                top: 10px;
                left: 10px;
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 10px;
                border-radius: 4px;
                font-size: 12px;
                font-family: monospace;
                pointer-events: none;
                z-index: 10;
            `;
            
            const viewerContainer = document.querySelector('.viewer-container');
            if (viewerContainer) {
                viewerContainer.appendChild(infoPanel);
            }
        }
        
        infoPanel.innerHTML = `
            <div>Patient: ${dicomImage.patientInfo.name}</div>
            <div>Study: ${dicomImage.studyInfo.description}</div>
            <div>Size: ${dicomImage.width} × ${dicomImage.height}</div>
            <div>WL: ${this.windowLevel} WW: ${this.windowWidth}</div>
            <div>Slice: ${this.currentSlice + 1}/${this.imageStack.length}</div>
        `;
    }
    
    generateSampleDICOM() {
        // Generate a sample DICOM for demonstration
        const sampleDICOM = this.simulateDICOMParsing(new ArrayBuffer(0), 'sample_brain.dcm');
        this.currentImage = sampleDICOM;
        this.originalImageData = sampleDICOM.imageData;
        this.imageStack = [sampleDICOM];
        this.displayImage(sampleDICOM);
        this.updateSliceControls();
    }
    
    exportImage() {
        if (!this.canvas) return;
        
        // Create download link
        const link = document.createElement('a');
        link.download = 'dicom_image.png';
        link.href = this.canvas.toDataURL();
        link.click();
    }
    
    resetImage() {
        this.brightness = 0;
        this.contrast = 100;
        this.windowLevel = this.currentImage?.windowCenter || 2048;
        this.windowWidth = this.currentImage?.windowWidth || 1024;
        
        // Reset sliders
        const sliders = ['brightness-slider', 'contrast-slider', 'window-level-slider', 'window-width-slider'];
        sliders.forEach(id => {
            const slider = document.getElementById(id);
            if (slider) {
                switch (id) {
                    case 'brightness-slider':
                        slider.value = 0;
                        break;
                    case 'contrast-slider':
                        slider.value = 100;
                        break;
                    case 'window-level-slider':
                        slider.value = this.windowLevel;
                        break;
                    case 'window-width-slider':
                        slider.value = this.windowWidth;
                        break;
                }
            }
        });
        
        this.updateDisplay();
    }
    
    showError(message) {
        console.error(message);
        // You could show a toast notification here
    }
    
    // Image analysis methods
    calculateImageStatistics() {
        if (!this.currentImage) return null;
        
        const data = this.currentImage.imageData;
        let min = Infinity;
        let max = -Infinity;
        let sum = 0;
        
        for (let i = 0; i < data.length; i++) {
            const value = data[i];
            min = Math.min(min, value);
            max = Math.max(max, value);
            sum += value;
        }
        
        const mean = sum / data.length;
        
        // Calculate standard deviation
        let sumSquaredDiff = 0;
        for (let i = 0; i < data.length; i++) {
            const diff = data[i] - mean;
            sumSquaredDiff += diff * diff;
        }
        
        const stdDev = Math.sqrt(sumSquaredDiff / data.length);
        
        return {
            min: min,
            max: max,
            mean: mean.toFixed(2),
            stdDev: stdDev.toFixed(2),
            pixels: data.length
        };
    }
    
    detectAnomalies() {
        // Simple anomaly detection based on intensity thresholds
        if (!this.currentImage) return [];
        
        const data = this.currentImage.imageData;
        const width = this.currentImage.width;
        const height = this.currentImage.height;
        const anomalies = [];
        
        // Define threshold for potential tumors (higher intensity regions)
        const threshold = 2000;
        
        for (let y = 1; y < height - 1; y++) {
            for (let x = 1; x < width - 1; x++) {
                const index = y * width + x;
                const intensity = data[index];
                
                if (intensity > threshold) {
                    // Check if it's part of a larger region
                    const neighbors = [
                        data[(y-1) * width + x],
                        data[(y+1) * width + x],
                        data[y * width + (x-1)],
                        data[y * width + (x+1)]
                    ];
                    
                    const highNeighbors = neighbors.filter(n => n > threshold).length;
                    
                    if (highNeighbors >= 2) {
                        anomalies.push({ x, y, intensity });
                    }
                }
            }
        }
        
        return anomalies;
    }
}

// Export for use in other modules
export default DICOMProcessor;

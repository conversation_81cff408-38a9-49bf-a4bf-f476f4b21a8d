// Startup Script for 3D Brain Tumor Segmentation Platform
// This script ensures proper initialization and provides debugging information

(function() {
    'use strict';
    
    // Debug flag
    const DEBUG = true;
    
    function log(message, type = 'info') {
        if (!DEBUG) return;
        
        const timestamp = new Date().toLocaleTimeString();
        const prefix = `[${timestamp}] [${type.toUpperCase()}]`;
        
        switch (type) {
            case 'error':
                console.error(prefix, message);
                break;
            case 'warn':
                console.warn(prefix, message);
                break;
            case 'success':
                console.log(`%c${prefix} ${message}`, 'color: green; font-weight: bold;');
                break;
            default:
                console.log(prefix, message);
        }
    }
    
    // Check for required dependencies
    function checkDependencies() {
        const dependencies = [
            { name: 'THREE', global: 'THREE', required: true },
            { name: 'CodeMirror', global: 'CodeMirror', required: false },
            { name: 'Plotly', global: 'Plotly', required: false },
            { name: 'dat.GUI', global: 'dat', required: false }
        ];
        
        log('Checking dependencies...');
        
        dependencies.forEach(dep => {
            if (window[dep.global]) {
                log(`✓ ${dep.name} loaded successfully`, 'success');
            } else {
                const level = dep.required ? 'error' : 'warn';
                log(`✗ ${dep.name} not found`, level);
            }
        });
    }
    
    // Check if all custom classes are loaded
    function checkCustomClasses() {
        const classes = [
            'BrainViewer3D',
            'MATLABSimulator',
            'DICOMProcessor',
            'ExerciseManager',
            'UIController',
            'BrainSegmentationApp'
        ];
        
        log('Checking custom classes...');
        
        classes.forEach(className => {
            if (window[className]) {
                log(`✓ ${className} class loaded`, 'success');
            } else {
                log(`✗ ${className} class not found`, 'error');
            }
        });
    }
    
    // Initialize error handling
    function setupErrorHandling() {
        window.addEventListener('error', (e) => {
            log(`Global error: ${e.message} at ${e.filename}:${e.lineno}`, 'error');
            showUserError('An error occurred. Please check the console for details.');
        });
        
        window.addEventListener('unhandledrejection', (e) => {
            log(`Unhandled promise rejection: ${e.reason}`, 'error');
            showUserError('A promise was rejected. Please check the console for details.');
        });
    }
    
    // Show user-friendly error messages
    function showUserError(message) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-notification';
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #fee2e2;
            color: #991b1b;
            padding: 16px;
            border-radius: 8px;
            border: 1px solid #fecaca;
            max-width: 400px;
            z-index: 10000;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        `;
        errorDiv.innerHTML = `
            <div style="display: flex; align-items: center; gap: 8px;">
                <i class="fas fa-exclamation-triangle"></i>
                <span>${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" style="margin-left: auto; background: none; border: none; color: #991b1b; cursor: pointer;">×</button>
            </div>
        `;
        
        document.body.appendChild(errorDiv);
        
        // Auto remove after 10 seconds
        setTimeout(() => {
            if (errorDiv.parentElement) {
                errorDiv.remove();
            }
        }, 10000);
    }
    
    // Check browser compatibility
    function checkBrowserCompatibility() {
        const features = [
            { name: 'WebGL', check: () => !!window.WebGLRenderingContext },
            { name: 'ES6 Classes', check: () => typeof class {} === 'function' },
            { name: 'CSS Grid', check: () => CSS.supports('display', 'grid') },
            { name: 'Fetch API', check: () => typeof fetch === 'function' },
            { name: 'Local Storage', check: () => typeof Storage !== 'undefined' }
        ];
        
        log('Checking browser compatibility...');
        
        let compatible = true;
        features.forEach(feature => {
            try {
                if (feature.check()) {
                    log(`✓ ${feature.name} supported`, 'success');
                } else {
                    log(`✗ ${feature.name} not supported`, 'error');
                    compatible = false;
                }
            } catch (e) {
                log(`✗ ${feature.name} check failed: ${e.message}`, 'error');
                compatible = false;
            }
        });
        
        if (!compatible) {
            showUserError('Your browser may not support all features. Please use a modern browser like Chrome, Firefox, or Safari.');
        }
        
        return compatible;
    }
    
    // Initialize the platform
    function initializePlatform() {
        log('Initializing 3D Brain Tumor Segmentation Platform...');
        
        // Check if DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializePlatform);
            return;
        }
        
        // Run checks
        checkBrowserCompatibility();
        checkDependencies();
        
        // Wait a bit for all scripts to load
        setTimeout(() => {
            checkCustomClasses();
            
            // Check if main app is initialized
            if (window.app) {
                log('✓ Main application initialized successfully', 'success');
            } else {
                log('✗ Main application not initialized', 'error');
            }
            
            // Show ready message
            if (window.app && window.app.isInitialized !== false) {
                log('🎉 Platform ready for use!', 'success');
                showReadyNotification();
            }
        }, 1000);
    }
    
    // Show ready notification
    function showReadyNotification() {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #dcfce7;
            color: #166534;
            padding: 16px;
            border-radius: 8px;
            border: 1px solid #bbf7d0;
            z-index: 10000;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            animation: slideInUp 0.3s ease-out;
        `;
        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 8px;">
                <i class="fas fa-check-circle"></i>
                <span>Platform ready! Start exploring the modules.</span>
                <button onclick="this.parentElement.parentElement.remove()" style="margin-left: auto; background: none; border: none; color: #166534; cursor: pointer;">×</button>
            </div>
        `;
        
        // Add animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInUp {
                from { transform: translateY(100%); opacity: 0; }
                to { transform: translateY(0); opacity: 1; }
            }
        `;
        document.head.appendChild(style);
        
        document.body.appendChild(notification);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.style.animation = 'slideInUp 0.3s ease-in reverse';
                setTimeout(() => notification.remove(), 300);
            }
        }, 5000);
    }
    
    // Add helpful global functions for debugging
    window.debugPlatform = {
        checkDependencies,
        checkCustomClasses,
        checkBrowserCompatibility,
        log,
        showUserError,
        restart: () => {
            location.reload();
        },
        getStatus: () => {
            return {
                app: !!window.app,
                brainViewer: !!window.brainViewer,
                matlabSimulator: !!window.matlabSimulator,
                dicomProcessor: !!window.dicomProcessor,
                exerciseManager: !!window.exerciseManager,
                uiController: !!window.uiController
            };
        }
    };
    
    // Setup error handling
    setupErrorHandling();
    
    // Start initialization
    initializePlatform();
    
    log('Startup script loaded');
    
})();

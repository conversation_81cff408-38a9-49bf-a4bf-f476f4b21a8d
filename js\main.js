// Main Application Controller for 3D Brain Tumor Segmentation Platform

import BrainViewer3D from './3d-viewer.js';
import MATLABSimulator from './matlab-simulator.js';

class BrainSegmentationApp {
    constructor() {
        this.brainViewer = null;
        this.matlabSimulator = null;
        this.currentSection = 'overview';
        this.userProgress = {
            completedExercises: 0,
            totalScore: 0,
            sectionsVisited: new Set(),
            timeSpent: 0
        };
        
        this.init();
    }
    
    init() {
        this.showLoadingScreen();
        
        // Initialize components after a short delay to show loading animation
        setTimeout(() => {
            this.initializeComponents();
            this.setupEventListeners();
            this.setupNavigation();
            this.hideLoadingScreen();
            this.updateProgressDisplay();
        }, 2000);
    }
    
    showLoadingScreen() {
        const loadingScreen = document.getElementById('loading-screen');
        const app = document.getElementById('app');
        
        if (loadingScreen) loadingScreen.style.display = 'flex';
        if (app) app.style.display = 'none';
    }
    
    hideLoadingScreen() {
        const loadingScreen = document.getElementById('loading-screen');
        const app = document.getElementById('app');
        
        if (loadingScreen) {
            loadingScreen.style.opacity = '0';
            setTimeout(() => {
                loadingScreen.style.display = 'none';
                if (app) app.style.display = 'block';
            }, 500);
        }
    }
    
    initializeComponents() {
        try {
            // Initialize MATLAB Simulator
            this.matlabSimulator = new MATLABSimulator();
            console.log('MATLAB Simulator initialized');
            
            // Initialize 3D Brain Viewer (will be created when 3D section is accessed)
            console.log('Application components ready');
            
        } catch (error) {
            console.error('Error initializing components:', error);
            this.showError('Failed to initialize application components');
        }
    }
    
    setupEventListeners() {
        // Navigation buttons
        const navButtons = document.querySelectorAll('.nav-btn');
        navButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const section = e.target.dataset.section;
                this.navigateToSection(section);
            });
        });
        
        // Feature card navigation buttons
        const cardButtons = document.querySelectorAll('.card-btn[data-navigate]');
        cardButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const section = e.target.dataset.navigate;
                this.navigateToSection(section);
            });
        });
        
        // Tool buttons
        const toolButtons = document.querySelectorAll('.tool-btn');
        toolButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.handleToolAction(e.target.id);
            });
        });
        
        // Exercise items
        const exerciseItems = document.querySelectorAll('.exercise-item');
        exerciseItems.forEach(item => {
            item.addEventListener('click', (e) => {
                const exerciseId = e.currentTarget.dataset.exercise;
                this.loadExercise(exerciseId);
            });
        });
        
        // Modal close
        const modalClose = document.querySelector('.modal-close');
        if (modalClose) {
            modalClose.addEventListener('click', () => {
                this.closeModal();
            });
        }
        
        // Window events
        window.addEventListener('beforeunload', () => {
            this.saveProgress();
        });
        
        // Track time spent
        this.startTimeTracking();
    }
    
    setupNavigation() {
        // Set initial section
        this.navigateToSection('overview');
        
        // Update section menu based on current section
        this.updateSectionMenu();
    }
    
    navigateToSection(sectionId) {
        // Hide all sections
        const sections = document.querySelectorAll('.content-section');
        sections.forEach(section => {
            section.classList.remove('active');
        });
        
        // Show target section
        const targetSection = document.getElementById(`${sectionId}-section`);
        if (targetSection) {
            targetSection.classList.add('active');
        }
        
        // Update navigation buttons
        const navButtons = document.querySelectorAll('.nav-btn');
        navButtons.forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.section === sectionId) {
                btn.classList.add('active');
            }
        });
        
        // Initialize section-specific components
        this.initializeSectionComponents(sectionId);
        
        // Update progress
        this.userProgress.sectionsVisited.add(sectionId);
        this.currentSection = sectionId;
        this.updateSectionMenu();
        this.updateProgressDisplay();
        
        console.log(`Navigated to section: ${sectionId}`);
    }
    
    initializeSectionComponents(sectionId) {
        switch (sectionId) {
            case '3d-segmentation':
                if (!this.brainViewer) {
                    this.brainViewer = new BrainViewer3D('three-js-container');
                    
                    // Update analysis results periodically
                    setInterval(() => {
                        if (this.brainViewer) {
                            this.brainViewer.updateAnalysisResults();
                        }
                    }, 1000);
                }
                break;
                
            case 'matlab-basics':
                // MATLAB simulator is already initialized
                break;
                
            case 'dicom-processing':
                this.initializeDICOMProcessor();
                break;
                
            case 'exercises':
                this.loadExerciseList();
                break;
        }
    }
    
    initializeDICOMProcessor() {
        const fileInput = document.getElementById('dicom-file-input');
        const canvas = document.getElementById('dicom-canvas');
        
        if (fileInput && canvas) {
            fileInput.addEventListener('change', (e) => {
                this.handleDICOMUpload(e.target.files);
            });
            
            // Setup DICOM controls
            this.setupDICOMControls();
        }
    }
    
    setupDICOMControls() {
        const controls = [
            'brightness-slider',
            'contrast-slider',
            'window-level-slider',
            'window-width-slider',
            'slice-slider'
        ];
        
        controls.forEach(controlId => {
            const control = document.getElementById(controlId);
            if (control) {
                control.addEventListener('input', (e) => {
                    this.updateDICOMDisplay(controlId, e.target.value);
                });
            }
        });
    }
    
    handleDICOMUpload(files) {
        if (files.length === 0) return;
        
        const file = files[0];
        const reader = new FileReader();
        
        reader.onload = (e) => {
            try {
                // In a real implementation, you would use a DICOM parser library
                // For this demo, we'll simulate DICOM processing
                this.simulateDICOMProcessing(e.target.result);
            } catch (error) {
                console.error('Error processing DICOM file:', error);
                this.showError('Failed to process DICOM file');
            }
        };
        
        reader.readAsArrayBuffer(file);
    }
    
    simulateDICOMProcessing(arrayBuffer) {
        // Simulate DICOM processing
        const canvas = document.getElementById('dicom-canvas');
        const ctx = canvas.getContext('2d');
        
        // Create a sample medical image
        canvas.width = 512;
        canvas.height = 512;
        
        // Generate sample brain scan image
        const imageData = ctx.createImageData(512, 512);
        const data = imageData.data;
        
        for (let i = 0; i < data.length; i += 4) {
            const x = (i / 4) % 512;
            const y = Math.floor((i / 4) / 512);
            
            // Create brain-like pattern
            const centerX = 256;
            const centerY = 256;
            const distance = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);
            
            let intensity = 0;
            if (distance < 200) {
                intensity = 150 + Math.sin(distance * 0.1) * 50;
                // Add some noise
                intensity += (Math.random() - 0.5) * 30;
            }
            
            intensity = Math.max(0, Math.min(255, intensity));
            
            data[i] = intensity;     // Red
            data[i + 1] = intensity; // Green
            data[i + 2] = intensity; // Blue
            data[i + 3] = 255;       // Alpha
        }
        
        ctx.putImageData(imageData, 0, 0);
        
        // Update slice info
        document.getElementById('current-slice').textContent = '1';
        document.getElementById('total-slices').textContent = '1';
    }
    
    updateDICOMDisplay(controlId, value) {
        const canvas = document.getElementById('dicom-canvas');
        const ctx = canvas.getContext('2d');
        
        // Apply image processing based on control
        switch (controlId) {
            case 'brightness-slider':
                ctx.filter = `brightness(${100 + parseInt(value)}%)`;
                break;
            case 'contrast-slider':
                ctx.filter = `contrast(${value}%)`;
                break;
            // Add more image processing controls as needed
        }
        
        // Redraw the image with new settings
        // In a real implementation, you would reprocess the DICOM data
    }
    
    updateSectionMenu() {
        const sectionMenu = document.getElementById('section-menu');
        if (!sectionMenu) return;
        
        const menuContent = this.getSectionMenuContent(this.currentSection);
        sectionMenu.innerHTML = menuContent;
    }
    
    getSectionMenuContent(sectionId) {
        switch (sectionId) {
            case 'overview':
                return `
                    <h3><i class="fas fa-home"></i> Overview</h3>
                    <ul class="menu-list">
                        <li><a href="#" data-navigate="matlab-basics">MATLAB Basics</a></li>
                        <li><a href="#" data-navigate="dicom-processing">DICOM Processing</a></li>
                        <li><a href="#" data-navigate="3d-segmentation">3D Visualization</a></li>
                        <li><a href="#" data-navigate="exercises">Practice Exercises</a></li>
                    </ul>
                `;
                
            case 'matlab-basics':
                return `
                    <h3><i class="fas fa-code"></i> MATLAB Topics</h3>
                    <ul class="menu-list">
                        <li><a href="#" onclick="app.loadMATLABTopic('variables')">Variables & Operations</a></li>
                        <li><a href="#" onclick="app.loadMATLABTopic('arrays')">Arrays & Matrices</a></li>
                        <li><a href="#" onclick="app.loadMATLABTopic('functions')">Functions</a></li>
                        <li><a href="#" onclick="app.loadMATLABTopic('plotting')">Plotting</a></li>
                        <li><a href="#" onclick="app.loadMATLABTopic('image-processing')">Image Processing</a></li>
                    </ul>
                `;
                
            case 'dicom-processing':
                return `
                    <h3><i class="fas fa-file-medical"></i> DICOM Tools</h3>
                    <ul class="menu-list">
                        <li><a href="#" onclick="app.loadDICOMTopic('basics')">DICOM Basics</a></li>
                        <li><a href="#" onclick="app.loadDICOMTopic('viewing')">Image Viewing</a></li>
                        <li><a href="#" onclick="app.loadDICOMTopic('processing')">Image Processing</a></li>
                        <li><a href="#" onclick="app.loadDICOMTopic('analysis')">Image Analysis</a></li>
                    </ul>
                `;
                
            case '3d-segmentation':
                return `
                    <h3><i class="fas fa-cube"></i> 3D Tools</h3>
                    <ul class="menu-list">
                        <li><a href="#" onclick="app.load3DTopic('viewing')">3D Viewing</a></li>
                        <li><a href="#" onclick="app.load3DTopic('segmentation')">Tumor Segmentation</a></li>
                        <li><a href="#" onclick="app.load3DTopic('measurement')">Measurements</a></li>
                        <li><a href="#" onclick="app.load3DTopic('analysis')">Volume Analysis</a></li>
                    </ul>
                `;
                
            case 'exercises':
                return `
                    <h3><i class="fas fa-tasks"></i> Exercise Categories</h3>
                    <ul class="menu-list">
                        <li><a href="#" onclick="app.filterExercises('matlab')">MATLAB Fundamentals</a></li>
                        <li><a href="#" onclick="app.filterExercises('image')">Image Processing</a></li>
                        <li><a href="#" onclick="app.filterExercises('3d')">3D Visualization</a></li>
                        <li><a href="#" onclick="app.filterExercises('advanced')">Advanced Topics</a></li>
                    </ul>
                `;
                
            default:
                return '<p>Select a section to see available options.</p>';
        }
    }
    
    handleToolAction(toolId) {
        switch (toolId) {
            case 'reset-view':
                if (this.brainViewer) {
                    this.brainViewer.setView('iso');
                }
                break;
                
            case 'toggle-wireframe':
                if (this.brainViewer) {
                    // Toggle wireframe mode
                    console.log('Wireframe mode toggled');
                }
                break;
                
            case 'export-model':
                this.exportModel();
                break;
        }
    }
    
    exportModel() {
        // Simulate model export
        const link = document.createElement('a');
        link.href = 'data:text/plain;charset=utf-8,Brain tumor segmentation model exported';
        link.download = 'brain_tumor_model.txt';
        link.click();
        
        this.showNotification('Model exported successfully!', 'success');
    }
    
    loadExercise(exerciseId) {
        // Load specific exercise content
        console.log(`Loading exercise: ${exerciseId}`);
        
        // Update exercise workspace
        const exerciseContent = document.getElementById('exercise-content');
        if (exerciseContent) {
            exerciseContent.innerHTML = this.getExerciseContent(exerciseId);
        }
    }
    
    getExerciseContent(exerciseId) {
        const exercises = {
            'matlab-1': {
                title: 'Variables and Arrays',
                description: 'Learn basic variable operations and array manipulation',
                content: `
                    <h3>Exercise: Variables and Arrays</h3>
                    <p>Complete the following tasks:</p>
                    <ol>
                        <li>Create a variable 'x' with value 10</li>
                        <li>Create a variable 'y' with value 5</li>
                        <li>Calculate the sum and store in 'result'</li>
                        <li>Create an array with values [1, 2, 3, 4, 5]</li>
                    </ol>
                    <div class="exercise-code-area">
                        <textarea id="exercise-code" placeholder="Write your MATLAB code here..."></textarea>
                        <button onclick="app.checkExercise('matlab-1')">Check Solution</button>
                    </div>
                `
            },
            'matlab-2': {
                title: 'Matrix Operations',
                description: 'Practice matrix multiplication and transformations',
                content: `
                    <h3>Exercise: Matrix Operations</h3>
                    <p>Work with matrices:</p>
                    <ol>
                        <li>Create a 2x3 matrix A</li>
                        <li>Create a 3x2 matrix B</li>
                        <li>Multiply A and B</li>
                        <li>Find the transpose of the result</li>
                    </ol>
                    <div class="exercise-code-area">
                        <textarea id="exercise-code" placeholder="Write your MATLAB code here..."></textarea>
                        <button onclick="app.checkExercise('matlab-2')">Check Solution</button>
                    </div>
                `
            }
        };
        
        const exercise = exercises[exerciseId];
        return exercise ? exercise.content : '<p>Exercise not found.</p>';
    }
    
    checkExercise(exerciseId) {
        // Simulate exercise checking
        this.userProgress.completedExercises++;
        this.userProgress.totalScore += 10;
        this.updateProgressDisplay();
        this.showNotification('Exercise completed successfully!', 'success');
    }
    
    updateProgressDisplay() {
        const progressText = document.querySelector('.progress-text');
        const completedExercises = document.getElementById('completed-exercises');
        const totalScore = document.getElementById('total-score');
        
        const progressPercentage = Math.min(100, (this.userProgress.sectionsVisited.size / 5) * 100);
        
        if (progressText) {
            progressText.textContent = `${Math.round(progressPercentage)}%`;
        }
        
        if (completedExercises) {
            completedExercises.textContent = this.userProgress.completedExercises;
        }
        
        if (totalScore) {
            totalScore.textContent = this.userProgress.totalScore;
        }
        
        // Update progress circle
        const progressCircle = document.querySelector('.progress-circle');
        if (progressCircle) {
            const angle = (progressPercentage / 100) * 360;
            progressCircle.style.background = `conic-gradient(var(--primary-color) ${angle}deg, var(--bg-tertiary) ${angle}deg)`;
        }
    }
    
    startTimeTracking() {
        setInterval(() => {
            this.userProgress.timeSpent++;
        }, 1000);
    }
    
    saveProgress() {
        localStorage.setItem('brainSegmentationProgress', JSON.stringify(this.userProgress));
    }
    
    loadProgress() {
        const saved = localStorage.getItem('brainSegmentationProgress');
        if (saved) {
            this.userProgress = { ...this.userProgress, ...JSON.parse(saved) };
        }
    }
    
    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 6px;
            color: white;
            z-index: 1000;
            animation: slideIn 0.3s ease-out;
        `;
        
        switch (type) {
            case 'success':
                notification.style.backgroundColor = '#10b981';
                break;
            case 'error':
                notification.style.backgroundColor = '#ef4444';
                break;
            case 'warning':
                notification.style.backgroundColor = '#f97316';
                break;
            default:
                notification.style.backgroundColor = '#3b82f6';
        }
        
        document.body.appendChild(notification);
        
        // Remove after 3 seconds
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
    
    showError(message) {
        this.showNotification(message, 'error');
    }
    
    closeModal() {
        const modal = document.getElementById('exercise-modal');
        if (modal) {
            modal.classList.remove('active');
        }
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new BrainSegmentationApp();
});

// Export for use in other modules
export default BrainSegmentationApp;


import React from 'react';
import SectionCard from '../components/SectionCard';
import CodeBlock from '../components/CodeBlock';

const AdvancedExamples: React.FC = () => {
  return (
    <div>
      <h2 className="text-3xl font-extrabold text-slate-900 dark:text-white mb-6">💡 أمثلة تطبيقية متقدمة</h2>
      
      <SectionCard title="تحليل صور الدماغ بالرنين المغناطيسي (MRI)">
        <p>يمكن استخدام الماتلاب لتحديد حجم الأورام أو مناطق التلف في صور الرنين المغناطيسي للدماغ. يتضمن ذلك التجزئة، ثم حساب الخصائص المورفولوجية للمناطق المعزولة.</p>
        <CodeBlock code={`% ... بعد خطوة التجزئة والحصول على binary_mask ...\n\n% البحث عن المناطق المتصلة (الأورام المحتملة)\n[labeled_regions, num_regions] = bwlabel(binary_mask);\n\n% حساب خصائص كل منطقة\nregion_properties = regionprops(labeled_regions, 'Area', 'Centroid');\n\n% عرض مساحة أكبر منطقة تم العثور عليها\nif num_regions > 0\n    all_areas = [region_properties.Area];\n    [max_area, max_idx] = max(all_areas);\n    fprintf('مساحة أكبر منطقة مشبوهة: %d بكسل\\n', max_area);\nend`} />
      </SectionCard>

      <SectionCard title="كشف الكسور في الأشعة السينية (X-ray)">
        <p>يمكن تطوير خوارزميات للكشف عن الحواف والتغيرات الحادة في الكثافة العظمية، والتي قد تشير إلى وجود كسور. دوال كشف الحواف مثل 'edge' تكون مفيدة هنا.</p>
        <CodeBlock code={`% قراءة صورة أشعة سينية للعظام\nxray_image = imread('bone_xray.jpg');\ngray_image = rgb2gray(xray_image);\n\n% تطبيق كاشف الحواف Canny\nedges = edge(gray_image, 'canny', 0.1);\n\n% عرض الحواف المكتشفة\nfigure;\nimshow(edges);\ntitle('الحواف المكتشفة في صورة الأشعة');`} />
      </SectionCard>
      
      <SectionCard title="أنظمة التشخيص التلقائي (CAD)">
        <p>تمثل أنظمة التشخيص بمساعدة الحاسوب (CAD) قمة تطبيقات معالجة الصور الطبية. تجمع هذه الأنظمة بين معالجة الصور وتقنيات الذكاء الاصطناعي (مثل تعلم الآلة) لتحديد مناطق الاهتمام وتقديم رأي ثانٍ للطبيب.</p>
        <p className="mt-4">على سبيل المثال، يمكن تدريب مصنف (Classifier) للتمييز بين الأورام الحميدة والخبيثة بناءً على خصائص مستخرجة من الصور مثل الشكل، الحجم، والملمس (Texture).</p>
        <CodeBlock code={`% هذا الكود هو مجرد مثال توضيحي للفكرة\n\n% استخراج الخصائص من منطقة الورم\nfeatures = extract_features(tumor_region);\n\n% تحميل نموذج تعلم آلة مدرب مسبقًا\nload('tumor_classifier_model.mat');\n\n% التنبؤ بنوع الورم\nprediction = trained_model.predict(features);\n\ndisp(['التشخيص المتوقع: ', prediction]);`} />
      </SectionCard>
    </div>
  );
};

export default AdvancedExamples;

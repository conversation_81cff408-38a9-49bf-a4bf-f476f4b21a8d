import React from 'react';
import { SectionId } from '../types';
import Introduction from '../content/Introduction';
import MatlabBasics from '../content/MatlabBasics';
import DicomProcessing from '../content/DicomProcessing';
import AdvancedExamples from '../content/AdvancedExamples';
import DeepLearningSegmentation from '../content/DeepLearningSegmentation';

interface ContentDisplayProps {
  activeSection: SectionId;
}

const ContentDisplay: React.FC<ContentDisplayProps> = ({ activeSection }) => {
  const renderContent = () => {
    switch (activeSection) {
      case SectionId.Introduction:
        return <Introduction />;
      case SectionId.MatlabBasics:
        return <MatlabBasics />;
      case SectionId.DicomProcessing:
        return <DicomProcessing />;
      case SectionId.AdvancedExamples:
        return <AdvancedExamples />;
      case SectionId.DeepLearningSegmentation:
        return <DeepLearningSegmentation />;
      default:
        return <Introduction />;
    }
  };

  return <div className="w-full h-full">{renderContent()}</div>;
};

export default ContentDisplay;
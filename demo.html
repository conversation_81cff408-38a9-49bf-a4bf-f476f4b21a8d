<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Brain Tumor Segmentation Platform - Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #1e293b;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .demo-container {
            max-width: 800px;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            margin: 20px;
        }
        
        .demo-header {
            background: linear-gradient(135deg, #2563eb, #10b981);
            color: white;
            padding: 40px;
            text-align: center;
        }
        
        .demo-header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .demo-header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .demo-content {
            padding: 40px;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .feature-card {
            text-align: center;
            padding: 30px 20px;
            border-radius: 15px;
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            border-color: #2563eb;
            box-shadow: 0 10px 25px rgba(37, 99, 235, 0.1);
        }
        
        .feature-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #2563eb, #10b981);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 1.5rem;
            color: white;
        }
        
        .feature-card h3 {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 10px;
            color: #1e293b;
        }
        
        .feature-card p {
            color: #64748b;
            font-size: 0.95rem;
        }
        
        .demo-actions {
            text-align: center;
            margin-top: 40px;
        }
        
        .btn-primary {
            display: inline-block;
            background: linear-gradient(135deg, #2563eb, #10b981);
            color: white;
            padding: 15px 30px;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
            margin: 0 10px;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(37, 99, 235, 0.4);
        }
        
        .btn-secondary {
            display: inline-block;
            background: white;
            color: #2563eb;
            padding: 15px 30px;
            border: 2px solid #2563eb;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            margin: 0 10px;
        }
        
        .btn-secondary:hover {
            background: #2563eb;
            color: white;
            transform: translateY(-2px);
        }
        
        .tech-stack {
            background: #f1f5f9;
            padding: 30px;
            border-radius: 15px;
            margin-top: 40px;
        }
        
        .tech-stack h3 {
            text-align: center;
            margin-bottom: 20px;
            color: #1e293b;
        }
        
        .tech-list {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 15px;
        }
        
        .tech-item {
            background: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            color: #475569;
            border: 1px solid #e2e8f0;
        }
        
        .demo-footer {
            background: #1e293b;
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .demo-footer p {
            opacity: 0.8;
            margin-bottom: 15px;
        }
        
        .social-links {
            display: flex;
            justify-content: center;
            gap: 20px;
        }
        
        .social-links a {
            color: white;
            font-size: 1.5rem;
            opacity: 0.7;
            transition: opacity 0.3s ease;
        }
        
        .social-links a:hover {
            opacity: 1;
        }
        
        @media (max-width: 768px) {
            .demo-header h1 {
                font-size: 2rem;
            }
            
            .demo-header p {
                font-size: 1rem;
            }
            
            .demo-content {
                padding: 30px 20px;
            }
            
            .feature-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .btn-primary,
            .btn-secondary {
                display: block;
                margin: 10px 0;
            }
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
    </style>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1><i class="fas fa-brain"></i> 3D Brain Tumor Segmentation</h1>
            <p>Interactive Learning Platform for Medical Image Processing</p>
        </div>
        
        <div class="demo-content">
            <div class="feature-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-code"></i>
                    </div>
                    <h3>MATLAB Programming</h3>
                    <p>Interactive code editor with real-time execution and comprehensive tutorials</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-file-medical"></i>
                    </div>
                    <h3>DICOM Processing</h3>
                    <p>Medical image handling, visualization, and advanced processing techniques</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-cube"></i>
                    </div>
                    <h3>3D Visualization</h3>
                    <p>Interactive Three.js-based brain models with tumor segmentation tools</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-tasks"></i>
                    </div>
                    <h3>Interactive Exercises</h3>
                    <p>Progressive coding challenges with automated testing and instant feedback</p>
                </div>
            </div>
            
            <div class="tech-stack">
                <h3>🛠️ Built with Modern Web Technologies</h3>
                <div class="tech-list">
                    <span class="tech-item">Three.js</span>
                    <span class="tech-item">WebGL</span>
                    <span class="tech-item">CodeMirror</span>
                    <span class="tech-item">Plotly.js</span>
                    <span class="tech-item">ES6+ JavaScript</span>
                    <span class="tech-item">CSS Grid</span>
                    <span class="tech-item">Responsive Design</span>
                    <span class="tech-item">Accessibility</span>
                </div>
            </div>
            
            <div class="demo-actions">
                <a href="brain-segmentation.html" class="btn-primary pulse">
                    <i class="fas fa-rocket"></i> Launch Platform
                </a>
                <a href="#" onclick="showFeatures()" class="btn-secondary">
                    <i class="fas fa-info-circle"></i> Learn More
                </a>
            </div>
        </div>
        
        <div class="demo-footer">
            <p>🎓 Perfect for medical students, researchers, and healthcare professionals</p>
            <p>🌐 Works in any modern browser - no installation required!</p>
        </div>
    </div>
    
    <script>
        function showFeatures() {
            alert(`🧠 3D Brain Tumor Segmentation Platform Features:

✅ Interactive MATLAB Programming Environment
✅ Real-time 3D Brain Visualization
✅ DICOM Medical Image Processing
✅ Progressive Learning Exercises
✅ Automated Code Testing
✅ Progress Tracking System
✅ Responsive Design for All Devices
✅ Full Accessibility Support
✅ Dark/Light Theme Toggle
✅ Keyboard Shortcuts

🚀 Click "Launch Platform" to start learning!`);
        }
        
        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.feature-card');
            
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    card.style.transition = 'all 0.6s ease';
                    
                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 200);
            });
        });
    </script>
</body>
</html>

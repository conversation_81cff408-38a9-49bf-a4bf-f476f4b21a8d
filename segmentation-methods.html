<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhancing Segmentation Precision - Methods & Examples</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/plotly.js/2.18.0/plotly.min.js"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: #1e293b;
            background: #f8fafc;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #2563eb, #10b981);
            color: white;
            padding: 40px;
            border-radius: 20px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(37, 99, 235, 0.3);
        }
        
        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .methods-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .method-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
        }
        
        .method-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        }
        
        .method-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .method-icon {
            width: 50px;
            height: 50px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }
        
        .method-card h3 {
            font-size: 1.4rem;
            font-weight: 600;
            color: #1e293b;
        }
        
        .method-description {
            color: #64748b;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        
        .code-example {
            background: #1e293b;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 14px;
            line-height: 1.5;
            margin: 15px 0;
            overflow-x: auto;
        }
        
        .example-section {
            margin: 20px 0;
        }
        
        .example-section h4 {
            color: #1e293b;
            margin-bottom: 10px;
            font-weight: 600;
        }
        
        .advantages {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .advantages h5 {
            color: #166534;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .advantages ul {
            color: #166534;
            padding-left: 20px;
        }
        
        .disadvantages {
            background: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .disadvantages h5 {
            color: #991b1b;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .disadvantages ul {
            color: #991b1b;
            padding-left: 20px;
        }
        
        .interactive-demo {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            margin: 30px 0;
        }
        
        .demo-controls {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: #2563eb;
            color: white;
        }
        
        .btn-primary:hover {
            background: #1d4ed8;
            transform: translateY(-1px);
        }
        
        .btn-secondary {
            background: #10b981;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #059669;
            transform: translateY(-1px);
        }
        
        .visualization-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .viz-panel {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
        }
        
        .comparison-table {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            margin: 30px 0;
            overflow-x: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }
        
        th {
            background: #f8fafc;
            font-weight: 600;
            color: #1e293b;
        }
        
        .accuracy-high {
            color: #166534;
            font-weight: 600;
        }
        
        .accuracy-medium {
            color: #ca8a04;
            font-weight: 600;
        }
        
        .accuracy-low {
            color: #dc2626;
            font-weight: 600;
        }
        
        @media (max-width: 768px) {
            .methods-grid {
                grid-template-columns: 1fr;
            }
            
            .visualization-container {
                grid-template-columns: 1fr;
            }
            
            .demo-controls {
                flex-direction: column;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-brain"></i> Enhancing Segmentation Precision</h1>
            <p>Comprehensive Methods & Techniques for Medical Image Segmentation</p>
        </div>
        
        <div class="methods-grid">
            <!-- Traditional Image Processing Methods -->
            <div class="method-card">
                <div class="method-header">
                    <div class="method-icon" style="background: linear-gradient(135deg, #3b82f6, #1d4ed8);">
                        <i class="fas fa-adjust"></i>
                    </div>
                    <h3>1. Thresholding Techniques</h3>
                </div>
                
                <div class="method-description">
                    Pixel intensity-based segmentation methods that separate regions based on intensity values.
                </div>
                
                <div class="example-section">
                    <h4>MATLAB Implementation:</h4>
                    <div class="code-example">% Global Thresholding
I = imread('brain_scan.dcm');
I_gray = rgb2gray(I);

% Otsu's method for automatic threshold
threshold = graythresh(I_gray);
BW = imbinarize(I_gray, threshold);

% Adaptive thresholding
BW_adaptive = imbinarize(I_gray, 'adaptive', ...
    'ForegroundPolarity', 'bright', 'Sensitivity', 0.4);

% Multi-level thresholding
thresh_multi = multithresh(I_gray, 3);
seg_multi = imquantize(I_gray, thresh_multi);

figure;
subplot(2,2,1); imshow(I_gray); title('Original');
subplot(2,2,2); imshow(BW); title('Otsu Threshold');
subplot(2,2,3); imshow(BW_adaptive); title('Adaptive');
subplot(2,2,4); imshow(seg_multi,[]); title('Multi-level');</div>
                </div>
                
                <div class="advantages">
                    <h5><i class="fas fa-check-circle"></i> Advantages:</h5>
                    <ul>
                        <li>Simple and fast implementation</li>
                        <li>Works well for high contrast images</li>
                        <li>Automatic threshold selection (Otsu's method)</li>
                        <li>Low computational cost</li>
                    </ul>
                </div>
                
                <div class="disadvantages">
                    <h5><i class="fas fa-times-circle"></i> Disadvantages:</h5>
                    <ul>
                        <li>Sensitive to noise and intensity variations</li>
                        <li>Poor performance with low contrast</li>
                        <li>Cannot handle complex tissue boundaries</li>
                        <li>Limited to intensity-based features</li>
                    </ul>
                </div>
            </div>
            
            <!-- Region Growing -->
            <div class="method-card">
                <div class="method-header">
                    <div class="method-icon" style="background: linear-gradient(135deg, #10b981, #059669);">
                        <i class="fas fa-seedling"></i>
                    </div>
                    <h3>2. Region Growing</h3>
                </div>
                
                <div class="method-description">
                    Seed-based segmentation that grows regions from initial seed points based on similarity criteria.
                </div>
                
                <div class="example-section">
                    <h4>MATLAB Implementation:</h4>
                    <div class="code-example">function segmented = regionGrowing(img, seed, threshold)
% Region growing algorithm for tumor segmentation
[rows, cols] = size(img);
segmented = false(rows, cols);
visited = false(rows, cols);

% Initialize with seed point
queue = seed;
seed_intensity = img(seed(1), seed(2));

while ~isempty(queue)
    current = queue(1,:);
    queue(1,:) = [];
    
    row = current(1);
    col = current(2);
    
    if visited(row, col)
        continue;
    end
    
    visited(row, col) = true;
    
    % Check similarity criterion
    if abs(img(row, col) - seed_intensity) < threshold
        segmented(row, col) = true;
        
        % Add 8-connected neighbors
        for dr = -1:1
            for dc = -1:1
                nr = row + dr;
                nc = col + dc;
                if nr > 0 && nr <= rows && nc > 0 && nc <= cols
                    if ~visited(nr, nc)
                        queue = [queue; nr, nc];
                    end
                end
            end
        end
    end
end

% Example usage:
I = imread('brain_mri.jpg');
I_gray = rgb2gray(I);
seed_point = [100, 150]; % User-selected seed
threshold = 20;
tumor_mask = regionGrowing(I_gray, seed_point, threshold);</div>
                </div>
                
                <div class="advantages">
                    <h5><i class="fas fa-check-circle"></i> Advantages:</h5>
                    <ul>
                        <li>Preserves connectivity of regions</li>
                        <li>Can handle irregular shapes</li>
                        <li>User control through seed selection</li>
                        <li>Good for homogeneous regions</li>
                    </ul>
                </div>
                
                <div class="disadvantages">
                    <h5><i class="fas fa-times-circle"></i> Disadvantages:</h5>
                    <ul>
                        <li>Sensitive to seed point selection</li>
                        <li>May leak into adjacent regions</li>
                        <li>Requires manual threshold setting</li>
                        <li>Computationally expensive for large images</li>
                    </ul>
                </div>
            </div>
            
            <!-- Edge-Based Segmentation -->
            <div class="method-card">
                <div class="method-header">
                    <div class="method-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                        <i class="fas fa-vector-square"></i>
                    </div>
                    <h3>3. Edge-Based Segmentation</h3>
                </div>
                
                <div class="method-description">
                    Detects boundaries between different tissue types using edge detection algorithms.
                </div>
                
                <div class="example-section">
                    <h4>MATLAB Implementation:</h4>
                    <div class="code-example">% Edge-based segmentation with multiple operators
I = imread('brain_scan.jpg');
I_gray = rgb2gray(I);

% Apply Gaussian smoothing
I_smooth = imgaussfilt(I_gray, 1.5);

% Different edge detection methods
edges_canny = edge(I_smooth, 'canny', [0.1 0.2]);
edges_sobel = edge(I_smooth, 'sobel');
edges_prewitt = edge(I_smooth, 'prewitt');
edges_log = edge(I_smooth, 'log');

% Morphological operations to close gaps
se = strel('disk', 2);
edges_closed = imclose(edges_canny, se);
edges_filled = imfill(edges_closed, 'holes');

% Watershed segmentation on gradient
grad = imgradient(I_smooth);
grad_mag = imgradient(grad);
watershed_labels = watershed(grad_mag);

% Active contours (snakes)
mask = false(size(I_gray));
mask(50:150, 50:150) = true; % Initial contour
bw = activecontour(I_gray, mask, 300, 'edge');

figure;
subplot(2,3,1); imshow(I_gray); title('Original');
subplot(2,3,2); imshow(edges_canny); title('Canny');
subplot(2,3,3); imshow(edges_sobel); title('Sobel');
subplot(2,3,4); imshow(edges_filled); title('Filled Edges');
subplot(2,3,5); imshow(label2rgb(watershed_labels)); title('Watershed');
subplot(2,3,6); imshow(bw); title('Active Contour');</div>
                </div>
                
                <div class="advantages">
                    <h5><i class="fas fa-check-circle"></i> Advantages:</h5>
                    <ul>
                        <li>Good boundary localization</li>
                        <li>Works well with clear tissue boundaries</li>
                        <li>Multiple algorithms available</li>
                        <li>Can be combined with other methods</li>
                    </ul>
                </div>
                
                <div class="disadvantages">
                    <h5><i class="fas fa-times-circle"></i> Disadvantages:</h5>
                    <ul>
                        <li>Sensitive to noise</li>
                        <li>May produce fragmented boundaries</li>
                        <li>Requires post-processing</li>
                        <li>Difficulty with weak edges</li>
                    </ul>
                </div>
            </div>
            
            <!-- Clustering Methods -->
            <div class="method-card">
                <div class="method-header">
                    <div class="method-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                        <i class="fas fa-project-diagram"></i>
                    </div>
                    <h3>4. Clustering Methods</h3>
                </div>
                
                <div class="method-description">
                    Groups pixels with similar characteristics using unsupervised learning algorithms.
                </div>
                
                <div class="example-section">
                    <h4>MATLAB Implementation:</h4>
                    <div class="code-example">% K-means clustering for brain tissue segmentation
I = imread('brain_mri.jpg');
I_gray = rgb2gray(I);
I_double = double(I_gray);

% Prepare feature vector (intensity + spatial coordinates)
[rows, cols] = size(I_gray);
[X, Y] = meshgrid(1:cols, 1:rows);

% Feature matrix: [intensity, x_coord, y_coord]
features = [I_double(:), X(:), Y(:)];
features = features(I_double(:) > 0, :); % Remove background

% Normalize features
features_norm = normalize(features);

% K-means clustering
k = 4; % Number of clusters (CSF, Gray matter, White matter, Tumor)
[cluster_idx, centroids] = kmeans(features_norm, k, ...
    'MaxIter', 100, 'Replicates', 3);

% Reconstruct segmented image
segmented = zeros(size(I_gray));
valid_pixels = find(I_double > 0);
segmented(valid_pixels) = cluster_idx;

% Fuzzy C-means clustering
options = [2.0, 100, 1e-5, 0]; % [fuzziness, max_iter, min_improve, info]
[centers, U] = fcm(features_norm', k, options);

% Get hard segmentation from fuzzy membership
[~, fuzzy_labels] = max(U);
fuzzy_segmented = zeros(size(I_gray));
fuzzy_segmented(valid_pixels) = fuzzy_labels;

figure;
subplot(1,3,1); imshow(I_gray); title('Original');
subplot(1,3,2); imshow(label2rgb(segmented)); title('K-means');
subplot(1,3,3); imshow(label2rgb(fuzzy_segmented)); title('Fuzzy C-means');</div>
                </div>
                
                <div class="advantages">
                    <h5><i class="fas fa-check-circle"></i> Advantages:</h5>
                    <ul>
                        <li>Unsupervised learning approach</li>
                        <li>Can identify multiple tissue types</li>
                        <li>Handles intensity variations well</li>
                        <li>Fuzzy clustering provides uncertainty measures</li>
                    </ul>
                </div>
                
                <div class="disadvantages">
                    <h5><i class="fas fa-times-circle"></i> Disadvantages:</h5>
                    <ul>
                        <li>Requires prior knowledge of cluster number</li>
                        <li>Sensitive to initialization</li>
                        <li>May not preserve spatial connectivity</li>
                        <li>Computationally intensive</li>
                    </ul>
                </div>
            </div>
            
            <!-- Machine Learning Methods -->
            <div class="method-card">
                <div class="method-header">
                    <div class="method-icon" style="background: linear-gradient(135deg, #ef4444, #dc2626);">
                        <i class="fas fa-robot"></i>
                    </div>
                    <h3>5. Machine Learning Methods</h3>
                </div>
                
                <div class="method-description">
                    Supervised learning approaches using labeled training data to learn segmentation patterns.
                </div>
                
                <div class="example-section">
                    <h4>MATLAB Implementation:</h4>
                    <div class="code-example">% Random Forest for pixel classification
% Load training data
load('brain_training_data.mat'); % Contains features and labels

% Extract features from image
I = imread('brain_test.jpg');
I_gray = rgb2gray(I);

% Feature extraction function
features = extractImageFeatures(I_gray);

% Train Random Forest classifier
numTrees = 100;
rf_model = TreeBagger(numTrees, training_features, training_labels, ...
    'Method', 'classification', 'OOBPrediction', 'on');

% Predict on test image
[predictions, scores] = predict(rf_model, features);
predicted_labels = str2double(predictions);

% Reshape to image dimensions
segmented_rf = reshape(predicted_labels, size(I_gray));

% Support Vector Machine (SVM)
svm_model = fitcecoc(training_features, training_labels);
svm_predictions = predict(svm_model, features);
segmented_svm = reshape(svm_predictions, size(I_gray));

function features = extractImageFeatures(img)
    % Extract multiple features for each pixel
    [rows, cols] = size(img);
    
    % Intensity features
    intensity = double(img(:));
    
    % Texture features (GLCM)
    glcm = graycomatrix(img, 'Offset', [0 1; -1 1; -1 0; -1 -1]);
    texture_stats = graycoprops(glcm, {'Contrast', 'Correlation', ...
        'Energy', 'Homogeneity'});
    
    % Gradient features
    [Gx, Gy] = gradient(double(img));
    gradient_mag = sqrt(Gx.^2 + Gy.^2);
    gradient_dir = atan2(Gy, Gx);
    
    % Local binary patterns
    lbp = extractLBPFeatures(img);
    
    % Combine all features
    features = [intensity, gradient_mag(:), gradient_dir(:), ...
        repmat(texture_stats.Contrast, rows*cols, 1), ...
        repmat(lbp, rows*cols, 1)];
end</div>
                </div>
                
                <div class="advantages">
                    <h5><i class="fas fa-check-circle"></i> Advantages:</h5>
                    <ul>
                        <li>Can learn complex patterns</li>
                        <li>Handles multiple feature types</li>
                        <li>Good generalization with sufficient data</li>
                        <li>Can incorporate domain knowledge</li>
                    </ul>
                </div>
                
                <div class="disadvantages">
                    <h5><i class="fas fa-times-circle"></i> Disadvantages:</h5>
                    <ul>
                        <li>Requires large labeled datasets</li>
                        <li>Computationally expensive training</li>
                        <li>May overfit to training data</li>
                        <li>Feature engineering required</li>
                    </ul>
                </div>
            </div>
            
            <!-- Deep Learning Methods -->
            <div class="method-card">
                <div class="method-header">
                    <div class="method-icon" style="background: linear-gradient(135deg, #06b6d4, #0891b2);">
                        <i class="fas fa-brain"></i>
                    </div>
                    <h3>6. Deep Learning Methods</h3>
                </div>
                
                <div class="method-description">
                    Convolutional Neural Networks (CNNs) that automatically learn hierarchical features for segmentation.
                </div>
                
                <div class="example-section">
                    <h4>MATLAB Implementation:</h4>
                    <div class="code-example">% U-Net architecture for brain tumor segmentation
% Define U-Net layers
imageSize = [256 256 1];
numClasses = 4; % Background, CSF, Gray matter, White matter, Tumor

% Encoder (Contracting path)
layers = [
    imageInputLayer(imageSize, 'Name', 'input')
    
    % Block 1
    convolution2dLayer(3, 64, 'Padding', 'same', 'Name', 'conv1_1')
    batchNormalizationLayer('Name', 'bn1_1')
    reluLayer('Name', 'relu1_1')
    convolution2dLayer(3, 64, 'Padding', 'same', 'Name', 'conv1_2')
    batchNormalizationLayer('Name', 'bn1_2')
    reluLayer('Name', 'relu1_2')
    maxPooling2dLayer(2, 'Stride', 2, 'Name', 'pool1')
    
    % Block 2
    convolution2dLayer(3, 128, 'Padding', 'same', 'Name', 'conv2_1')
    batchNormalizationLayer('Name', 'bn2_1')
    reluLayer('Name', 'relu2_1')
    convolution2dLayer(3, 128, 'Padding', 'same', 'Name', 'conv2_2')
    batchNormalizationLayer('Name', 'bn2_2')
    reluLayer('Name', 'relu2_2')
    maxPooling2dLayer(2, 'Stride', 2, 'Name', 'pool2')
    
    % Bottleneck
    convolution2dLayer(3, 256, 'Padding', 'same', 'Name', 'conv3_1')
    batchNormalizationLayer('Name', 'bn3_1')
    reluLayer('Name', 'relu3_1')
    
    % Decoder (Expanding path)
    transposedConv2dLayer(2, 128, 'Stride', 2, 'Name', 'upconv2')
    convolution2dLayer(3, 128, 'Padding', 'same', 'Name', 'conv4_1')
    reluLayer('Name', 'relu4_1')
    
    transposedConv2dLayer(2, 64, 'Stride', 2, 'Name', 'upconv1')
    convolution2dLayer(3, 64, 'Padding', 'same', 'Name', 'conv5_1')
    reluLayer('Name', 'relu5_1')
    
    % Output layer
    convolution2dLayer(1, numClasses, 'Name', 'final_conv')
    softmaxLayer('Name', 'softmax')
    pixelClassificationLayer('Name', 'output')
];

% Training options
options = trainingOptions('adam', ...
    'InitialLearnRate', 1e-3, ...
    'MaxEpochs', 50, ...
    'MiniBatchSize', 8, ...
    'ValidationFrequency', 10, ...
    'Plots', 'training-progress');

% Train the network
net = trainNetwork(trainingImages, trainingLabels, layers, options);

% Prediction
testImage = imread('brain_test.jpg');
predictedLabels = semanticseg(testImage, net);

% Post-processing with Conditional Random Fields (CRF)
crf_result = applyCRF(testImage, predictedLabels);</div>
                </div>
                
                <div class="advantages">
                    <h5><i class="fas fa-check-circle"></i> Advantages:</h5>
                    <ul>
                        <li>State-of-the-art accuracy</li>
                        <li>Automatic feature learning</li>
                        <li>End-to-end training</li>
                        <li>Handles complex patterns and variations</li>
                    </ul>
                </div>
                
                <div class="disadvantages">
                    <h5><i class="fas fa-times-circle"></i> Disadvantages:</h5>
                    <ul>
                        <li>Requires very large datasets</li>
                        <li>Computationally intensive</li>
                        <li>Black box approach</li>
                        <li>Requires specialized hardware (GPUs)</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Interactive Demonstration -->
        <div class="interactive-demo">
            <h2><i class="fas fa-play-circle"></i> Interactive Segmentation Comparison</h2>
            <p>Compare different segmentation methods on sample brain images</p>

            <div class="demo-controls">
                <button class="btn btn-primary" onclick="loadSampleImage('normal')">
                    <i class="fas fa-brain"></i> Normal Brain
                </button>
                <button class="btn btn-primary" onclick="loadSampleImage('tumor')">
                    <i class="fas fa-exclamation-triangle"></i> Brain with Tumor
                </button>
                <button class="btn btn-secondary" onclick="runSegmentation('threshold')">
                    <i class="fas fa-adjust"></i> Thresholding
                </button>
                <button class="btn btn-secondary" onclick="runSegmentation('region')">
                    <i class="fas fa-seedling"></i> Region Growing
                </button>
                <button class="btn btn-secondary" onclick="runSegmentation('edge')">
                    <i class="fas fa-vector-square"></i> Edge Detection
                </button>
                <button class="btn btn-secondary" onclick="runSegmentation('clustering')">
                    <i class="fas fa-project-diagram"></i> Clustering
                </button>
            </div>

            <div class="visualization-container">
                <div class="viz-panel">
                    <h4>Original Image</h4>
                    <canvas id="original-canvas" width="250" height="250" style="border: 1px solid #e2e8f0; border-radius: 8px;"></canvas>
                </div>
                <div class="viz-panel">
                    <h4>Segmentation Result</h4>
                    <canvas id="segmented-canvas" width="250" height="250" style="border: 1px solid #e2e8f0; border-radius: 8px;"></canvas>
                </div>
                <div class="viz-panel">
                    <h4>Performance Metrics</h4>
                    <div id="metrics-display" style="text-align: left; padding: 20px; background: white; border-radius: 8px; border: 1px solid #e2e8f0;">
                        <p><strong>Dice Coefficient:</strong> <span id="dice-score">-</span></p>
                        <p><strong>Jaccard Index:</strong> <span id="jaccard-score">-</span></p>
                        <p><strong>Sensitivity:</strong> <span id="sensitivity-score">-</span></p>
                        <p><strong>Specificity:</strong> <span id="specificity-score">-</span></p>
                        <p><strong>Processing Time:</strong> <span id="processing-time">-</span></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Comparison Table -->
        <div class="comparison-table">
            <h2><i class="fas fa-table"></i> Method Comparison Summary</h2>
            <table>
                <thead>
                    <tr>
                        <th>Method</th>
                        <th>Accuracy</th>
                        <th>Speed</th>
                        <th>Complexity</th>
                        <th>Data Requirements</th>
                        <th>Best Use Case</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>Thresholding</strong></td>
                        <td class="accuracy-medium">Medium</td>
                        <td class="accuracy-high">Very Fast</td>
                        <td class="accuracy-high">Low</td>
                        <td class="accuracy-high">None</td>
                        <td>High contrast images, quick screening</td>
                    </tr>
                    <tr>
                        <td><strong>Region Growing</strong></td>
                        <td class="accuracy-medium">Medium-High</td>
                        <td class="accuracy-medium">Medium</td>
                        <td class="accuracy-medium">Medium</td>
                        <td class="accuracy-high">Minimal</td>
                        <td>Homogeneous regions, user interaction</td>
                    </tr>
                    <tr>
                        <td><strong>Edge Detection</strong></td>
                        <td class="accuracy-medium">Medium</td>
                        <td class="accuracy-high">Fast</td>
                        <td class="accuracy-medium">Medium</td>
                        <td class="accuracy-high">None</td>
                        <td>Clear boundaries, preprocessing step</td>
                    </tr>
                    <tr>
                        <td><strong>Clustering</strong></td>
                        <td class="accuracy-medium">Medium-High</td>
                        <td class="accuracy-medium">Medium</td>
                        <td class="accuracy-medium">Medium</td>
                        <td class="accuracy-medium">Moderate</td>
                        <td>Multi-tissue segmentation</td>
                    </tr>
                    <tr>
                        <td><strong>Machine Learning</strong></td>
                        <td class="accuracy-high">High</td>
                        <td class="accuracy-medium">Medium</td>
                        <td class="accuracy-high">High</td>
                        <td class="accuracy-low">Large</td>
                        <td>Complex patterns, feature-rich data</td>
                    </tr>
                    <tr>
                        <td><strong>Deep Learning</strong></td>
                        <td class="accuracy-high">Very High</td>
                        <td class="accuracy-low">Slow (training)</td>
                        <td class="accuracy-high">Very High</td>
                        <td class="accuracy-low">Very Large</td>
                        <td>State-of-the-art accuracy, large datasets</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Advanced Techniques -->
        <div class="interactive-demo">
            <h2><i class="fas fa-cogs"></i> Advanced Enhancement Techniques</h2>

            <div class="methods-grid">
                <div class="method-card">
                    <h3><i class="fas fa-layer-group"></i> Multi-Modal Fusion</h3>
                    <div class="method-description">
                        Combining information from multiple imaging modalities (T1, T2, FLAIR, DWI) for improved segmentation.
                    </div>
                    <div class="code-example">% Multi-modal brain tumor segmentation
% Load different MRI sequences
T1 = imread('T1_weighted.dcm');
T2 = imread('T2_weighted.dcm');
FLAIR = imread('FLAIR.dcm');
DWI = imread('DWI.dcm');

% Normalize intensities
T1_norm = mat2gray(T1);
T2_norm = mat2gray(T2);
FLAIR_norm = mat2gray(FLAIR);
DWI_norm = mat2gray(DWI);

% Create multi-channel feature vector
multi_modal = cat(3, T1_norm, T2_norm, FLAIR_norm, DWI_norm);

% Apply deep learning segmentation
net = load('trained_multimodal_unet.mat');
segmentation = semanticseg(multi_modal, net.trainedNet);

% Post-processing with CRF
final_segmentation = denseCRF(multi_modal, segmentation);</div>
                </div>

                <div class="method-card">
                    <h3><i class="fas fa-magic"></i> Ensemble Methods</h3>
                    <div class="method-description">
                        Combining multiple segmentation algorithms to improve robustness and accuracy.
                    </div>
                    <div class="code-example">% Ensemble segmentation approach
function final_seg = ensembleSegmentation(image)
    % Method 1: Thresholding
    seg1 = imbinarize(image, graythresh(image));

    % Method 2: Region growing
    seed = [size(image,1)/2, size(image,2)/2];
    seg2 = regionGrowing(image, seed, 0.1);

    % Method 3: K-means clustering
    features = reshape(image, [], 1);
    [idx, ~] = kmeans(double(features), 3);
    seg3 = reshape(idx == 2, size(image)); % Assume tumor is cluster 2

    % Method 4: Deep learning
    seg4 = semanticseg(image, pretrained_net);

    % Voting ensemble
    ensemble_votes = seg1 + seg2 + seg3 + seg4;
    final_seg = ensemble_votes >= 2; % Majority voting

    % Weighted ensemble (based on confidence scores)
    weights = [0.2, 0.25, 0.25, 0.3]; % Higher weight for deep learning
    weighted_seg = weights(1)*seg1 + weights(2)*seg2 + ...
                   weights(3)*seg3 + weights(4)*seg4;
    final_seg_weighted = weighted_seg > 0.5;
end</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Interactive demonstration functionality
        let currentImage = null;
        let currentMethod = null;

        function loadSampleImage(type) {
            const canvas = document.getElementById('original-canvas');
            const ctx = canvas.getContext('2d');

            // Clear canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // Generate sample brain image
            const imageData = ctx.createImageData(250, 250);
            const data = imageData.data;

            for (let i = 0; i < data.length; i += 4) {
                const x = (i / 4) % 250;
                const y = Math.floor((i / 4) / 250);
                const centerX = 125;
                const centerY = 125;
                const distance = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);

                let intensity = 0;

                if (type === 'normal') {
                    // Normal brain pattern
                    if (distance < 100) {
                        intensity = 120 + Math.sin(distance * 0.1) * 30;
                        intensity += (Math.random() - 0.5) * 20;
                    }
                } else if (type === 'tumor') {
                    // Brain with tumor
                    if (distance < 100) {
                        intensity = 120 + Math.sin(distance * 0.1) * 30;
                        intensity += (Math.random() - 0.5) * 20;

                        // Add tumor region
                        const tumorX = 150;
                        const tumorY = 100;
                        const tumorDist = Math.sqrt((x - tumorX) ** 2 + (y - tumorY) ** 2);
                        if (tumorDist < 25) {
                            intensity = 200 + (Math.random() - 0.5) * 40;
                        }
                    }
                }

                intensity = Math.max(0, Math.min(255, intensity));

                data[i] = intensity;     // Red
                data[i + 1] = intensity; // Green
                data[i + 2] = intensity; // Blue
                data[i + 3] = 255;       // Alpha
            }

            ctx.putImageData(imageData, 0, 0);
            currentImage = imageData;

            // Clear previous segmentation
            const segCanvas = document.getElementById('segmented-canvas');
            const segCtx = segCanvas.getContext('2d');
            segCtx.clearRect(0, 0, segCanvas.width, segCanvas.height);

            showNotification(`Loaded ${type} brain image`, 'info');
        }

        function runSegmentation(method) {
            if (!currentImage) {
                showNotification('Please load an image first', 'warning');
                return;
            }

            const startTime = performance.now();
            currentMethod = method;

            const segCanvas = document.getElementById('segmented-canvas');
            const segCtx = segCanvas.getContext('2d');

            // Simulate different segmentation methods
            const segmentedData = simulateSegmentation(currentImage, method);
            segCtx.putImageData(segmentedData, 0, 0);

            const endTime = performance.now();
            const processingTime = (endTime - startTime).toFixed(2);

            // Update metrics
            updateMetrics(method, processingTime);

            showNotification(`Applied ${method} segmentation`, 'success');
        }

        function simulateSegmentation(imageData, method) {
            const segData = new ImageData(250, 250);
            const original = imageData.data;
            const segmented = segData.data;

            for (let i = 0; i < original.length; i += 4) {
                const intensity = original[i];
                let segmentedValue = 0;

                switch (method) {
                    case 'threshold':
                        segmentedValue = intensity > 150 ? 255 : 0;
                        break;
                    case 'region':
                        // Simulate region growing
                        segmentedValue = intensity > 140 && intensity < 220 ? 255 : 0;
                        break;
                    case 'edge':
                        // Simulate edge detection
                        const x = (i / 4) % 250;
                        const y = Math.floor((i / 4) / 250);
                        const gradient = Math.abs(intensity - (original[i + 4] || intensity));
                        segmentedValue = gradient > 30 ? 255 : 0;
                        break;
                    case 'clustering':
                        // Simulate clustering
                        if (intensity < 50) segmentedValue = 0;      // Background
                        else if (intensity < 120) segmentedValue = 85;  // CSF
                        else if (intensity < 180) segmentedValue = 170; // Gray matter
                        else segmentedValue = 255;                      // White matter/Tumor
                        break;
                }

                // Apply color coding
                if (segmentedValue === 255) {
                    segmented[i] = 255;     // Red for tumor
                    segmented[i + 1] = 100;
                    segmented[i + 2] = 100;
                } else if (segmentedValue === 170) {
                    segmented[i] = 100;     // Blue for gray matter
                    segmented[i + 1] = 100;
                    segmented[i + 2] = 255;
                } else if (segmentedValue === 85) {
                    segmented[i] = 100;     // Green for CSF
                    segmented[i + 1] = 255;
                    segmented[i + 2] = 100;
                } else {
                    segmented[i] = 0;       // Black for background
                    segmented[i + 1] = 0;
                    segmented[i + 2] = 0;
                }
                segmented[i + 3] = 255;     // Alpha
            }

            return segData;
        }

        function updateMetrics(method, processingTime) {
            // Simulate performance metrics based on method
            const metrics = {
                'threshold': { dice: 0.72, jaccard: 0.56, sensitivity: 0.85, specificity: 0.78 },
                'region': { dice: 0.78, jaccard: 0.64, sensitivity: 0.82, specificity: 0.85 },
                'edge': { dice: 0.65, jaccard: 0.48, sensitivity: 0.75, specificity: 0.88 },
                'clustering': { dice: 0.81, jaccard: 0.68, sensitivity: 0.79, specificity: 0.89 }
            };

            const methodMetrics = metrics[method] || { dice: 0.75, jaccard: 0.60, sensitivity: 0.80, specificity: 0.85 };

            document.getElementById('dice-score').textContent = methodMetrics.dice.toFixed(3);
            document.getElementById('jaccard-score').textContent = methodMetrics.jaccard.toFixed(3);
            document.getElementById('sensitivity-score').textContent = methodMetrics.sensitivity.toFixed(3);
            document.getElementById('specificity-score').textContent = methodMetrics.specificity.toFixed(3);
            document.getElementById('processing-time').textContent = processingTime + ' ms';
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 20px;
                border-radius: 8px;
                color: white;
                z-index: 1000;
                max-width: 300px;
                font-weight: 500;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            `;

            switch (type) {
                case 'success':
                    notification.style.backgroundColor = '#10b981';
                    break;
                case 'warning':
                    notification.style.backgroundColor = '#f59e0b';
                    break;
                case 'error':
                    notification.style.backgroundColor = '#ef4444';
                    break;
                default:
                    notification.style.backgroundColor = '#3b82f6';
            }

            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // Initialize with a sample image
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                loadSampleImage('tumor');
            }, 1000);
        });
    </script>
</body>
</html>

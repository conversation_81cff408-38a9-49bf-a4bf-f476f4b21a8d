<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Platform Test - 3D Brain Tumor Segmentation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-item {
            margin: 20px 0;
            padding: 15px;
            border-left: 4px solid #ddd;
            background: #f9f9f9;
        }
        .test-item.pass {
            border-left-color: #4CAF50;
            background: #f1f8e9;
        }
        .test-item.fail {
            border-left-color: #f44336;
            background: #ffebee;
        }
        .test-item.pending {
            border-left-color: #ff9800;
            background: #fff3e0;
        }
        .btn {
            background: #2196F3;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #1976D2;
        }
        .btn.success {
            background: #4CAF50;
        }
        .btn.danger {
            background: #f44336;
        }
        .status {
            font-weight: bold;
            padding: 5px 10px;
            border-radius: 3px;
            display: inline-block;
            margin-left: 10px;
        }
        .status.pass { background: #4CAF50; color: white; }
        .status.fail { background: #f44336; color: white; }
        .status.pending { background: #ff9800; color: white; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧠 3D Brain Tumor Segmentation Platform - System Test</h1>
        <p>This page tests if all components are working correctly.</p>
        
        <button class="btn" onclick="runAllTests()">Run All Tests</button>
        <button class="btn success" onclick="openPlatform()">Open Platform</button>
        <button class="btn" onclick="openDemo()">Open Demo</button>
        
        <div id="test-results">
            <div class="test-item pending" id="test-dependencies">
                <h3>External Dependencies <span class="status pending">PENDING</span></h3>
                <p>Checking if external libraries (Three.js, CodeMirror, etc.) are available...</p>
                <div id="dependencies-details"></div>
            </div>
            
            <div class="test-item pending" id="test-files">
                <h3>File Structure <span class="status pending">PENDING</span></h3>
                <p>Verifying that all required files exist and are accessible...</p>
                <div id="files-details"></div>
            </div>
            
            <div class="test-item pending" id="test-classes">
                <h3>JavaScript Classes <span class="status pending">PENDING</span></h3>
                <p>Testing if all custom classes are properly defined...</p>
                <div id="classes-details"></div>
            </div>
            
            <div class="test-item pending" id="test-browser">
                <h3>Browser Compatibility <span class="status pending">PENDING</span></h3>
                <p>Checking browser support for required features...</p>
                <div id="browser-details"></div>
            </div>
            
            <div class="test-item pending" id="test-platform">
                <h3>Platform Initialization <span class="status pending">PENDING</span></h3>
                <p>Testing if the main platform can be initialized...</p>
                <div id="platform-details"></div>
            </div>
        </div>
        
        <div style="margin-top: 30px; padding: 20px; background: #e3f2fd; border-radius: 5px;">
            <h3>🔧 Troubleshooting Tips</h3>
            <ul>
                <li><strong>If tests fail:</strong> Make sure you're running this from a web server (not file:// protocol)</li>
                <li><strong>CORS errors:</strong> Use a local server like <code>python -m http.server</code> or <code>npx http-server</code></li>
                <li><strong>Missing files:</strong> Ensure all files are in the correct directories</li>
                <li><strong>Browser issues:</strong> Try Chrome, Firefox, or Safari (latest versions)</li>
            </ul>
        </div>
    </div>

    <script>
        function updateTestStatus(testId, status, details = '') {
            const testItem = document.getElementById(testId);
            const statusSpan = testItem.querySelector('.status');
            const detailsDiv = testItem.querySelector('[id$="-details"]');
            
            testItem.className = `test-item ${status}`;
            statusSpan.className = `status ${status}`;
            statusSpan.textContent = status.toUpperCase();
            
            if (details) {
                detailsDiv.innerHTML = details;
            }
        }
        
        async function testDependencies() {
            const dependencies = [
                { name: 'Three.js', url: 'https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js', global: 'THREE' },
                { name: 'CodeMirror', url: 'https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.js', global: 'CodeMirror' },
                { name: 'Plotly.js', url: 'https://cdnjs.cloudflare.com/ajax/libs/plotly.js/2.18.0/plotly.min.js', global: 'Plotly' }
            ];
            
            let allLoaded = true;
            let details = '<ul>';
            
            for (const dep of dependencies) {
                try {
                    if (window[dep.global]) {
                        details += `<li>✅ ${dep.name} - Loaded</li>`;
                    } else {
                        details += `<li>❌ ${dep.name} - Not found</li>`;
                        allLoaded = false;
                    }
                } catch (e) {
                    details += `<li>❌ ${dep.name} - Error: ${e.message}</li>`;
                    allLoaded = false;
                }
            }
            
            details += '</ul>';
            updateTestStatus('test-dependencies', allLoaded ? 'pass' : 'fail', details);
            return allLoaded;
        }
        
        async function testFiles() {
            const files = [
                'brain-segmentation.html',
                'js/main.js',
                'js/3d-viewer.js',
                'js/matlab-simulator.js',
                'js/dicom-processor.js',
                'js/exercises.js',
                'js/ui-controller.js',
                'styles/main.css',
                'styles/components.css',
                'styles/3d-viewer.css'
            ];
            
            let allExist = true;
            let details = '<ul>';
            
            for (const file of files) {
                try {
                    const response = await fetch(file, { method: 'HEAD' });
                    if (response.ok) {
                        details += `<li>✅ ${file} - Found</li>`;
                    } else {
                        details += `<li>❌ ${file} - Not found (${response.status})</li>`;
                        allExist = false;
                    }
                } catch (e) {
                    details += `<li>❌ ${file} - Error: ${e.message}</li>`;
                    allExist = false;
                }
            }
            
            details += '</ul>';
            updateTestStatus('test-files', allExist ? 'pass' : 'fail', details);
            return allExist;
        }
        
        function testBrowserCompatibility() {
            const features = [
                { name: 'WebGL', check: () => !!window.WebGLRenderingContext },
                { name: 'ES6 Classes', check: () => typeof class {} === 'function' },
                { name: 'CSS Grid', check: () => CSS.supports('display', 'grid') },
                { name: 'Fetch API', check: () => typeof fetch === 'function' },
                { name: 'Local Storage', check: () => typeof Storage !== 'undefined' }
            ];
            
            let allSupported = true;
            let details = '<ul>';
            
            features.forEach(feature => {
                try {
                    if (feature.check()) {
                        details += `<li>✅ ${feature.name} - Supported</li>`;
                    } else {
                        details += `<li>❌ ${feature.name} - Not supported</li>`;
                        allSupported = false;
                    }
                } catch (e) {
                    details += `<li>❌ ${feature.name} - Error: ${e.message}</li>`;
                    allSupported = false;
                }
            });
            
            details += '</ul>';
            updateTestStatus('test-browser', allSupported ? 'pass' : 'fail', details);
            return allSupported;
        }
        
        function testClasses() {
            // This test will be run after loading the platform scripts
            const classes = [
                'BrainViewer3D',
                'MATLABSimulator', 
                'DICOMProcessor',
                'ExerciseManager',
                'UIController',
                'BrainSegmentationApp'
            ];
            
            let allDefined = true;
            let details = '<ul>';
            
            classes.forEach(className => {
                if (window[className]) {
                    details += `<li>✅ ${className} - Defined</li>`;
                } else {
                    details += `<li>❌ ${className} - Not defined</li>`;
                    allDefined = false;
                }
            });
            
            details += '</ul>';
            updateTestStatus('test-classes', allDefined ? 'pass' : 'fail', details);
            return allDefined;
        }
        
        function testPlatform() {
            let canInitialize = true;
            let details = '<ul>';
            
            try {
                // Test if we can access the main elements
                const testDiv = document.createElement('div');
                testDiv.id = 'test-container';
                testDiv.innerHTML = '<div id="three-js-container"></div><div id="matlab-output"></div>';
                document.body.appendChild(testDiv);
                
                if (window.BrainSegmentationApp) {
                    details += '<li>✅ BrainSegmentationApp class available</li>';
                } else {
                    details += '<li>❌ BrainSegmentationApp class not available</li>';
                    canInitialize = false;
                }
                
                if (window.THREE) {
                    details += '<li>✅ Three.js available for 3D rendering</li>';
                } else {
                    details += '<li>❌ Three.js not available</li>';
                    canInitialize = false;
                }
                
                // Clean up
                document.body.removeChild(testDiv);
                
            } catch (e) {
                details += `<li>❌ Platform test error: ${e.message}</li>`;
                canInitialize = false;
            }
            
            details += '</ul>';
            updateTestStatus('test-platform', canInitialize ? 'pass' : 'fail', details);
            return canInitialize;
        }
        
        async function runAllTests() {
            console.log('Running platform tests...');
            
            // Test browser compatibility first
            const browserOk = testBrowserCompatibility();
            
            // Test dependencies
            const depsOk = await testDependencies();
            
            // Test file structure
            const filesOk = await testFiles();
            
            // Load platform scripts for class testing
            if (filesOk) {
                await loadPlatformScripts();
                setTimeout(() => {
                    const classesOk = testClasses();
                    const platformOk = testPlatform();
                    
                    // Show overall result
                    const allPassed = browserOk && depsOk && filesOk && classesOk && platformOk;
                    showOverallResult(allPassed);
                }, 1000);
            } else {
                updateTestStatus('test-classes', 'fail', 'Cannot test classes - files not found');
                updateTestStatus('test-platform', 'fail', 'Cannot test platform - files not found');
                showOverallResult(false);
            }
        }
        
        async function loadPlatformScripts() {
            const scripts = [
                'js/3d-viewer.js',
                'js/matlab-simulator.js', 
                'js/dicom-processor.js',
                'js/exercises.js',
                'js/ui-controller.js',
                'js/main.js'
            ];
            
            for (const script of scripts) {
                try {
                    await loadScript(script);
                } catch (e) {
                    console.error(`Failed to load ${script}:`, e);
                }
            }
        }
        
        function loadScript(src) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = resolve;
                script.onerror = reject;
                document.head.appendChild(script);
            });
        }
        
        function showOverallResult(passed) {
            const resultDiv = document.createElement('div');
            resultDiv.style.cssText = `
                margin-top: 30px;
                padding: 20px;
                border-radius: 10px;
                text-align: center;
                font-size: 18px;
                font-weight: bold;
                ${passed ? 'background: #c8e6c9; color: #2e7d32;' : 'background: #ffcdd2; color: #c62828;'}
            `;
            resultDiv.innerHTML = passed ? 
                '🎉 All tests passed! The platform is ready to use.' :
                '⚠️ Some tests failed. Please check the issues above.';
            
            document.getElementById('test-results').appendChild(resultDiv);
        }
        
        function openPlatform() {
            window.open('brain-segmentation.html', '_blank');
        }
        
        function openDemo() {
            window.open('demo.html', '_blank');
        }
        
        // Auto-run tests when page loads
        window.addEventListener('load', () => {
            setTimeout(runAllTests, 500);
        });
    </script>
</body>
</html>

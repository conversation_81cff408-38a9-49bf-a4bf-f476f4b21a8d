<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Brain Tumor Segmentation Platform</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: #1e293b;
            background: #f8fafc;
        }

        .app-container {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .app-header {
            background: white;
            border-bottom: 1px solid #e2e8f0;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .logo-section i {
            font-size: 2rem;
            color: #2563eb;
        }

        .logo-section h1 {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1e293b;
        }

        .main-nav {
            display: flex;
            gap: 0.5rem;
        }

        .nav-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border: none;
            background: transparent;
            color: #64748b;
            font-weight: 500;
            border-radius: 0.5rem;
            cursor: pointer;
            transition: all 0.15s ease;
        }

        .nav-btn:hover {
            background: #f1f5f9;
            color: #1e293b;
        }

        .nav-btn.active {
            background: #2563eb;
            color: white;
        }

        .main-content {
            flex: 1;
            display: flex;
            max-width: 1400px;
            margin: 0 auto;
            width: 100%;
        }

        .sidebar {
            width: 280px;
            background: #f8fafc;
            border-right: 1px solid #e2e8f0;
            padding: 1.5rem;
            overflow-y: auto;
            height: calc(100vh - 80px);
        }

        .content-panels {
            flex: 1;
            padding: 2rem;
            overflow-y: auto;
            height: calc(100vh - 80px);
        }

        .content-section {
            display: none;
        }

        .content-section.active {
            display: block;
        }

        .section-header h2 {
            font-size: 2rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .section-header p {
            font-size: 1.125rem;
            color: #64748b;
            margin-bottom: 2rem;
        }

        .overview-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .feature-card {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 0.75rem;
            padding: 2rem;
            text-align: center;
            transition: all 0.15s ease;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .feature-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            border-color: #2563eb;
        }

        .card-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #2563eb, #10b981);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
        }

        .card-icon i {
            font-size: 1.5rem;
            color: white;
        }

        .feature-card h3 {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 0.5rem;
        }

        .feature-card p {
            color: #64748b;
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }

        .card-btn {
            background: #2563eb;
            color: white;
            border: none;
            padding: 0.5rem 1.5rem;
            border-radius: 0.5rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.15s ease;
        }

        .card-btn:hover {
            background: #1d4ed8;
            transform: translateY(-1px);
        }

        .split-layout {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            height: calc(100vh - 250px);
        }

        .tutorial-panel,
        .code-panel {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 0.75rem;
            overflow: hidden;
        }

        .panel-header {
            background: #f8fafc;
            padding: 1rem;
            border-bottom: 1px solid #e2e8f0;
            font-weight: 600;
            color: #1e293b;
        }

        .tutorial-content {
            padding: 1.5rem;
            height: calc(100% - 60px);
            overflow-y: auto;
        }

        .code-editor-container {
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .editor-controls {
            display: flex;
            gap: 0.5rem;
            padding: 1rem;
            background: #f8fafc;
            border-bottom: 1px solid #e2e8f0;
        }

        .btn-run,
        .btn-clear {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 0.375rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.15s ease;
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .btn-run {
            background: #10b981;
            color: white;
        }

        .btn-run:hover {
            background: #059669;
        }

        .btn-clear {
            background: #ef4444;
            color: white;
        }

        .btn-clear:hover {
            background: #dc2626;
        }

        .code-editor {
            flex: 1;
            border: none;
            outline: none;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            line-height: 1.5;
            padding: 1rem;
            background: #2d3748;
            color: #e2e8f0;
            resize: none;
        }

        .output-container {
            height: 200px;
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 0.75rem;
            overflow: hidden;
            margin-top: 1rem;
        }

        .output-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            background: #f8fafc;
            border-bottom: 1px solid #e2e8f0;
        }

        .code-output {
            padding: 1rem;
            height: calc(100% - 60px);
            overflow-y: auto;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            line-height: 1.5;
            background: #1a202c;
            color: #e2e8f0;
        }

        .output-line {
            margin-bottom: 0.25rem;
        }

        .output-line.success {
            color: #10b981;
        }

        .output-line.error {
            color: #ef4444;
        }

        .output-line.warning {
            color: #f59e0b;
        }

        .output-line.info {
            color: #3b82f6;
        }

        @media (max-width: 1024px) {
            .main-content {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                height: auto;
                border-right: none;
                border-bottom: 1px solid #e2e8f0;
            }

            .split-layout {
                grid-template-columns: 1fr;
                height: auto;
            }
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
                padding: 1rem;
            }

            .main-nav {
                flex-wrap: wrap;
                justify-content: center;
            }

            .overview-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <div class="app-header">
            <div class="header-content">
                <div class="logo-section">
                    <i class="fas fa-brain"></i>
                    <div>
                        <h1>3D Brain Tumor Segmentation</h1>
                        <div style="font-size: 0.875rem; color: #64748b; font-weight: 500;">Interactive Learning Platform</div>
                    </div>
                </div>

                <nav class="main-nav">
                    <button class="nav-btn active" data-section="overview">
                        <i class="fas fa-home"></i> Overview
                    </button>
                    <button class="nav-btn" data-section="matlab-basics">
                        <i class="fas fa-code"></i> MATLAB Basics
                    </button>
                    <button class="nav-btn" data-section="dicom-processing">
                        <i class="fas fa-file-medical"></i> DICOM Processing
                    </button>
                    <button class="nav-btn" data-section="3d-segmentation">
                        <i class="fas fa-cube"></i> 3D Segmentation
                    </button>
                    <button class="nav-btn" data-section="exercises">
                        <i class="fas fa-tasks"></i> Exercises
                    </button>
                    <a href="segmentation-methods.html" class="nav-btn" style="text-decoration: none;">
                        <i class="fas fa-microscope"></i> Methods Guide
                    </a>
                    <a href="realtime-3d-segmentation.html" class="nav-btn" style="text-decoration: none;">
                        <i class="fas fa-cube"></i> Real-time 3D
                    </a>
                    <a href="enhanced-realtime-3d.html" class="nav-btn" style="text-decoration: none; background: linear-gradient(135deg, #667eea, #764ba2); color: white;">
                        <i class="fas fa-rocket"></i> Enhanced 3D
                    </a>
                    <a href="ml-dl-methods.html" class="nav-btn" style="text-decoration: none; background: linear-gradient(135deg, #f093fb, #f5576c); color: white;">
                        <i class="fas fa-brain"></i> ML & DL Methods
                    </a>
                    <a href="mri-processing-gui.html" class="nav-btn" style="text-decoration: none; background: linear-gradient(135deg, #11998e, #38ef7d); color: white;">
                        <i class="fas fa-upload"></i> MRI Processing GUI
                    </a>
                </nav>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Sidebar -->
            <div class="sidebar">
                <div id="section-menu">
                    <h3><i class="fas fa-home"></i> Overview</h3>
                    <div style="padding: 1rem; background: #f8fafc; border-radius: 0.5rem; margin-top: 1rem;">
                        <p style="color: #64748b; font-size: 0.9rem;">Welcome to the 3D Brain Tumor Segmentation Platform!</p>
                        <p style="color: #64748b; font-size: 0.9rem; margin-top: 0.5rem;">Select a module from the navigation above to get started.</p>
                    </div>
                </div>
            </div>

            <!-- Content Panels -->
            <div class="content-panels">
                <!-- Overview Section -->
                <div id="overview-section" class="content-section active">
                    <div class="section-header">
                        <h2><i class="fas fa-home"></i> Platform Overview</h2>
                        <p>Welcome to the interactive 3D Brain Tumor Segmentation learning platform. Explore medical image processing, MATLAB programming, and 3D visualization.</p>
                    </div>

                    <div class="overview-grid">
                        <div class="feature-card">
                            <div class="card-icon">
                                <i class="fas fa-code"></i>
                            </div>
                            <h3>MATLAB Programming</h3>
                            <p>Learn MATLAB fundamentals with interactive tutorials, real-time code execution, and hands-on exercises.</p>
                            <button class="card-btn" data-navigate="matlab-basics">Start Learning</button>
                        </div>

                        <div class="feature-card">
                            <div class="card-icon">
                                <i class="fas fa-file-medical"></i>
                            </div>
                            <h3>DICOM Processing</h3>
                            <p>Master medical image processing with DICOM files, windowing, leveling, and advanced analysis techniques.</p>
                            <button class="card-btn" data-navigate="dicom-processing">Explore DICOM</button>
                        </div>

                        <div class="feature-card">
                            <div class="card-icon">
                                <i class="fas fa-cube"></i>
                            </div>
                            <h3>3D Visualization</h3>
                            <p>Experience interactive 3D brain models with tumor segmentation, volume analysis, and real-time manipulation.</p>
                            <button class="card-btn" data-navigate="3d-segmentation">View 3D Models</button>
                        </div>

                        <div class="feature-card">
                            <div class="card-icon">
                                <i class="fas fa-tasks"></i>
                            </div>
                            <h3>Interactive Exercises</h3>
                            <p>Practice your skills with progressive coding challenges, automated testing, and instant feedback.</p>
                            <button class="card-btn" data-navigate="exercises">Start Exercises</button>
                        </div>
                    </div>
                </div>

                <!-- MATLAB Basics Section -->
                <div id="matlab-basics-section" class="content-section">
                    <div class="section-header">
                        <h2><i class="fas fa-code"></i> MATLAB Programming Basics</h2>
                        <p>Learn MATLAB programming fundamentals with interactive tutorials and real-time code execution.</p>
                    </div>

                    <div class="split-layout">
                        <div class="tutorial-panel">
                            <div class="panel-header">Tutorial</div>
                            <div class="tutorial-content">
                                <h3>MATLAB Programming Tutorial</h3>
                                <div style="background: #f8fafc; padding: 1rem; border-radius: 0.5rem; margin: 1rem 0;">
                                    <h4>Getting Started with MATLAB</h4>
                                    <p>MATLAB is a powerful programming language for technical computing. Let's start with basic operations:</p>

                                    <h5 style="margin-top: 1rem;">1. Variables and Basic Operations</h5>
                                    <pre style="background: #1a202c; color: #e2e8f0; padding: 0.5rem; border-radius: 0.25rem; font-size: 0.875rem;">
% Create variables
x = 10;
y = 5;
result = x + y;
disp(result);</pre>

                                    <h5 style="margin-top: 1rem;">2. Arrays and Matrices</h5>
                                    <pre style="background: #1a202c; color: #e2e8f0; padding: 0.5rem; border-radius: 0.25rem; font-size: 0.875rem;">
% Create arrays
arr = [1, 2, 3, 4, 5];
matrix = [1, 2; 3, 4];
disp(matrix);</pre>

                                    <h5 style="margin-top: 1rem;">3. Plotting</h5>
                                    <pre style="background: #1a202c; color: #e2e8f0; padding: 0.5rem; border-radius: 0.25rem; font-size: 0.875rem;">
% Create a simple plot
t = 0:0.1:2*pi;
y = sin(t);
plot(t, y);
title('Sine Wave');</pre>

                                    <p style="margin-top: 1rem; color: #10b981; font-weight: 500;">
                                        💡 Try copying these examples to the code editor and click "Run" to see the results!
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div class="code-panel">
                            <div class="code-editor-container">
                                <div class="editor-controls">
                                    <button class="btn-run" id="run-matlab-code">
                                        <i class="fas fa-play"></i> Run
                                    </button>
                                    <button class="btn-clear" id="clear-matlab-code">
                                        <i class="fas fa-undo"></i> Clear
                                    </button>
                                </div>
                                <textarea id="matlab-editor" class="code-editor" placeholder="% Enter your MATLAB code here
% Example:
x = 10;
y = 5;
result = x + y;
disp(['Result: ', num2str(result)]);"></textarea>
                            </div>

                            <div class="output-container">
                                <div class="output-header">
                                    <span>Output</span>
                                    <button class="btn-clear" id="clear-output" style="background: none; border: none; color: #64748b; cursor: pointer;">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                                <div id="matlab-output" class="code-output"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- DICOM Processing Section -->
                <div id="dicom-processing-section" class="content-section">
                    <div class="section-header">
                        <h2><i class="fas fa-file-medical"></i> DICOM Image Processing</h2>
                        <p>Learn medical image processing with DICOM files, including visualization, windowing, and analysis.</p>
                    </div>

                    <div style="display: grid; grid-template-columns: 300px 1fr; gap: 2rem; height: calc(100vh - 250px);">
                        <div style="background: white; border: 1px solid #e2e8f0; border-radius: 0.75rem; padding: 1.5rem; overflow-y: auto;">
                            <h3>DICOM Controls</h3>

                            <div style="margin-bottom: 2rem;">
                                <h4 style="margin-bottom: 1rem;">File Upload</h4>
                                <label style="display: flex; flex-direction: column; align-items: center; gap: 1rem; padding: 2rem; border: 2px dashed #e2e8f0; border-radius: 0.75rem; cursor: pointer; transition: all 0.15s ease; text-align: center;">
                                    <i class="fas fa-upload" style="font-size: 2rem; color: #2563eb;"></i>
                                    <span>Click to upload DICOM files</span>
                                    <input type="file" id="dicom-file-input" multiple accept=".dcm,.dicom" style="display: none;">
                                </label>
                            </div>

                            <div>
                                <h4 style="margin-bottom: 1rem;">Image Processing</h4>

                                <div style="margin-bottom: 1rem;">
                                    <label style="display: block; font-size: 0.875rem; font-weight: 500; color: #64748b; margin-bottom: 0.5rem;">Brightness</label>
                                    <input type="range" id="brightness-slider" min="-100" max="100" value="0" style="width: 100%;">
                                </div>

                                <div style="margin-bottom: 1rem;">
                                    <label style="display: block; font-size: 0.875rem; font-weight: 500; color: #64748b; margin-bottom: 0.5rem;">Contrast</label>
                                    <input type="range" id="contrast-slider" min="0" max="200" value="100" style="width: 100%;">
                                </div>

                                <div style="margin-bottom: 1rem;">
                                    <label style="display: block; font-size: 0.875rem; font-weight: 500; color: #64748b; margin-bottom: 0.5rem;">Window Level</label>
                                    <input type="range" id="window-level-slider" min="0" max="4096" value="2048" style="width: 100%;">
                                </div>

                                <div style="margin-bottom: 1rem;">
                                    <label style="display: block; font-size: 0.875rem; font-weight: 500; color: #64748b; margin-bottom: 0.5rem;">Window Width</label>
                                    <input type="range" id="window-width-slider" min="1" max="4096" value="1024" style="width: 100%;">
                                </div>
                            </div>
                        </div>

                        <div style="background: white; border: 1px solid #e2e8f0; border-radius: 0.75rem; overflow: hidden;">
                            <div style="padding: 1rem; background: #f8fafc; border-bottom: 1px solid #e2e8f0; font-weight: 600;">
                                DICOM Viewer
                            </div>
                            <div style="position: relative; height: calc(100% - 60px);">
                                <canvas id="dicom-canvas" style="width: 100%; height: 100%; object-fit: contain; background: #000;"></canvas>
                                <div style="position: absolute; bottom: 0; left: 0; right: 0; background: rgba(0, 0, 0, 0.8); color: white; padding: 1rem;">
                                    <div style="display: flex; align-items: center; gap: 1rem;">
                                        <span>Slice:</span>
                                        <input type="range" id="slice-slider" min="1" max="1" value="1" style="flex: 1;">
                                        <span id="current-slice">1</span>/<span id="total-slices">1</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 3D Segmentation Section -->
                <div id="3d-segmentation-section" class="content-section">
                    <div class="section-header">
                        <h2><i class="fas fa-cube"></i> 3D Brain Tumor Segmentation</h2>
                        <p>Interactive 3D visualization and segmentation of brain tumors with real-time analysis.</p>
                    </div>

                    <div style="display: grid; grid-template-columns: 300px 1fr; gap: 2rem; height: calc(100vh - 250px);">
                        <div style="background: white; border: 1px solid #e2e8f0; border-radius: 0.75rem; padding: 1.5rem; overflow-y: auto; display: flex; flex-direction: column; gap: 2rem;">
                            <div>
                                <h3>Model Controls</h3>
                                <div style="margin-top: 1rem;">
                                    <div style="margin-bottom: 1rem;">
                                        <label style="display: block; font-size: 0.875rem; font-weight: 500; color: #64748b; margin-bottom: 0.5rem;">Brain Opacity</label>
                                        <input type="range" id="brain-opacity" min="0" max="100" value="80" style="width: 100%;">
                                    </div>

                                    <div style="margin-bottom: 1rem;">
                                        <label style="display: block; font-size: 0.875rem; font-weight: 500; color: #64748b; margin-bottom: 0.5rem;">Tumor Opacity</label>
                                        <input type="range" id="tumor-opacity" min="0" max="100" value="90" style="width: 100%;">
                                    </div>
                                </div>
                            </div>

                            <div>
                                <h3>Analysis Results</h3>
                                <div style="margin-top: 1rem;">
                                    <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                                        <span style="color: #64748b;">Tumor Volume:</span>
                                        <span style="font-weight: 600;">4.2 cm³</span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                                        <span style="color: #64748b;">Brain Volume:</span>
                                        <span style="font-weight: 600;">1400 cm³</span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                                        <span style="color: #64748b;">Tumor Ratio:</span>
                                        <span style="font-weight: 600;">0.3%</span>
                                    </div>
                                </div>

                                <div style="margin-top: 1rem; padding: 0.75rem; background: #f0f9ff; border-radius: 0.375rem; border: 1px solid #0ea5e9;">
                                    <p style="font-size: 0.875rem; color: #0369a1; margin: 0;">
                                        <i class="fas fa-info-circle"></i> Sample analysis data shown. Initialize 3D viewer for real-time calculations.
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div style="background: white; border: 1px solid #e2e8f0; border-radius: 0.75rem; overflow: hidden; position: relative;">
                            <div style="padding: 1rem; background: #f8fafc; border-bottom: 1px solid #e2e8f0; font-weight: 600;">
                                3D Brain Viewer
                            </div>
                            <div id="three-js-container" style="width: 100%; height: calc(100% - 120px); background: linear-gradient(135deg, #1e293b, #334155);">
                                <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: white; text-align: center;">
                                    <div>
                                        <i class="fas fa-cube" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.7;"></i>
                                        <p>3D Brain Viewer</p>
                                        <p style="font-size: 0.875rem; opacity: 0.7;">Click "Initialize 3D" to start the visualization</p>
                                    </div>
                                </div>
                            </div>

                            <div style="display: flex; justify-content: center; gap: 0.5rem; padding: 1rem; background: #f8fafc; border-top: 1px solid #e2e8f0;">
                                <button onclick="window.app && window.app.init3DViewer()" style="padding: 0.5rem 1rem; border: 1px solid #e2e8f0; background: white; color: #64748b; border-radius: 0.375rem; cursor: pointer; transition: all 0.15s ease;">Initialize 3D</button>
                                <button style="padding: 0.5rem 1rem; border: 1px solid #e2e8f0; background: white; color: #64748b; border-radius: 0.375rem; cursor: pointer; transition: all 0.15s ease;">Front</button>
                                <button style="padding: 0.5rem 1rem; border: 1px solid #e2e8f0; background: white; color: #64748b; border-radius: 0.375rem; cursor: pointer; transition: all 0.15s ease;">Side</button>
                                <button style="padding: 0.5rem 1rem; border: 1px solid #e2e8f0; background: white; color: #64748b; border-radius: 0.375rem; cursor: pointer; transition: all 0.15s ease;">Top</button>
                                <button style="padding: 0.5rem 1rem; border: 1px solid #e2e8f0; background: white; color: #64748b; border-radius: 0.375rem; cursor: pointer; transition: all 0.15s ease;">Isometric</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Exercises Section -->
                <div id="exercises-section" class="content-section">
                    <div class="section-header">
                        <h2><i class="fas fa-tasks"></i> Interactive Exercises</h2>
                        <p>Practice your MATLAB and image processing skills with hands-on coding challenges.</p>
                    </div>

                    <div style="display: grid; grid-template-columns: 300px 1fr; gap: 2rem; height: calc(100vh - 250px);">
                        <div style="background: white; border: 1px solid #e2e8f0; border-radius: 0.75rem; padding: 1.5rem; overflow-y: auto;">
                            <h3>Exercise Categories</h3>

                            <div style="margin-top: 1.5rem;">
                                <h4 style="color: #10b981; margin-bottom: 1rem;">MATLAB Fundamentals</h4>
                                <div class="exercise-item" data-exercise="matlab-1" style="padding: 1rem; border: 1px solid #e2e8f0; border-radius: 0.5rem; margin-bottom: 0.5rem; cursor: pointer; transition: all 0.15s ease;">
                                    <div style="display: flex; justify-content: space-between; align-items: center;">
                                        <div>
                                            <div style="font-weight: 500;">Variables and Arrays</div>
                                            <div style="font-size: 0.875rem; color: #64748b;">Basic operations</div>
                                        </div>
                                        <div class="exercise-status">
                                            <i class="fas fa-circle" style="color: #e2e8f0;"></i>
                                        </div>
                                    </div>
                                </div>

                                <div class="exercise-item" data-exercise="matlab-2" style="padding: 1rem; border: 1px solid #e2e8f0; border-radius: 0.5rem; margin-bottom: 0.5rem; cursor: pointer; transition: all 0.15s ease;">
                                    <div style="display: flex; justify-content: space-between; align-items: center;">
                                        <div>
                                            <div style="font-weight: 500;">Matrix Operations</div>
                                            <div style="font-size: 0.875rem; color: #64748b;">Linear algebra</div>
                                        </div>
                                        <div class="exercise-status">
                                            <i class="fas fa-circle" style="color: #e2e8f0;"></i>
                                        </div>
                                    </div>
                                </div>

                                <h4 style="color: #2563eb; margin: 1.5rem 0 1rem;">Image Processing</h4>
                                <div class="exercise-item" data-exercise="image-1" style="padding: 1rem; border: 1px solid #e2e8f0; border-radius: 0.5rem; margin-bottom: 0.5rem; cursor: pointer; transition: all 0.15s ease;">
                                    <div style="display: flex; justify-content: space-between; align-items: center;">
                                        <div>
                                            <div style="font-weight: 500;">Image Filtering</div>
                                            <div style="font-size: 0.875rem; color: #64748b;">Basic filters</div>
                                        </div>
                                        <div class="exercise-status">
                                            <i class="fas fa-circle" style="color: #e2e8f0;"></i>
                                        </div>
                                    </div>
                                </div>

                                <div class="exercise-item" data-exercise="image-2" style="padding: 1rem; border: 1px solid #e2e8f0; border-radius: 0.5rem; margin-bottom: 0.5rem; cursor: pointer; transition: all 0.15s ease;">
                                    <div style="display: flex; justify-content: space-between; align-items: center;">
                                        <div>
                                            <div style="font-weight: 500;">Tumor Detection</div>
                                            <div style="font-size: 0.875rem; color: #64748b;">Advanced analysis</div>
                                        </div>
                                        <div class="exercise-status">
                                            <i class="fas fa-circle" style="color: #e2e8f0;"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div style="background: white; border: 1px solid #e2e8f0; border-radius: 0.75rem; overflow: hidden;">
                            <div style="padding: 1rem; background: #f8fafc; border-bottom: 1px solid #e2e8f0; font-weight: 600;">
                                Exercise Workspace
                            </div>
                            <div id="exercise-content" style="padding: 2rem; height: calc(100% - 60px); overflow-y: auto;">
                                <div style="text-align: center; color: #64748b;">
                                    <i class="fas fa-rocket" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.7;"></i>
                                    <h3>Ready to Practice?</h3>
                                    <p>Select an exercise from the left panel to get started!</p>

                                    <div style="margin-top: 2rem; padding: 1.5rem; background: #f8fafc; border-radius: 0.75rem; text-align: left;">
                                        <h4 style="color: #1e293b; margin-bottom: 1rem;">Quick Start Guide:</h4>
                                        <ol style="color: #64748b; line-height: 1.6;">
                                            <li>Choose an exercise difficulty level (Easy, Medium, Hard)</li>
                                            <li>Read the exercise instructions carefully</li>
                                            <li>Write your MATLAB code in the editor</li>
                                            <li>Click "Run" to test your code</li>
                                            <li>Click "Check" to validate your solution</li>
                                            <li>Submit when all tests pass!</li>
                                        </ol>

                                        <div style="margin-top: 1rem; padding: 1rem; background: #dcfce7; border-radius: 0.5rem;">
                                            <p style="color: #166534; font-size: 0.875rem; margin: 0;">
                                                <i class="fas fa-lightbulb"></i> <strong>Tip:</strong> Start with the "Variables and Arrays" exercise to get familiar with the platform!
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Simple Application Controller
        class SimpleBrainApp {
            constructor() {
                this.currentSection = 'overview';
                this.init();
            }

            init() {
                console.log('Initializing 3D Brain Tumor Segmentation Platform...');
                this.setupNavigation();
                this.setupInteractions();
                console.log('Platform ready!');
            }

            setupNavigation() {
                const navButtons = document.querySelectorAll('.nav-btn');
                navButtons.forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        const section = e.target.dataset.section || e.target.closest('.nav-btn').dataset.section;
                        this.navigateToSection(section);
                    });
                });

                const cardButtons = document.querySelectorAll('.card-btn');
                cardButtons.forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        const section = e.target.dataset.navigate;
                        if (section) {
                            this.navigateToSection(section);
                        }
                    });
                });
            }

            navigateToSection(sectionId) {
                console.log(`Navigating to: ${sectionId}`);

                // Update navigation buttons
                const navButtons = document.querySelectorAll('.nav-btn');
                navButtons.forEach(btn => {
                    btn.classList.remove('active');
                    if (btn.dataset.section === sectionId) {
                        btn.classList.add('active');
                    }
                });

                // Update content sections
                const sections = document.querySelectorAll('.content-section');
                sections.forEach(section => {
                    section.classList.remove('active');
                });

                const targetSection = document.getElementById(`${sectionId}-section`);
                if (targetSection) {
                    targetSection.classList.add('active');
                }

                this.currentSection = sectionId;
                this.updateSectionMenu(sectionId);
            }

            updateSectionMenu(sectionId) {
                const sectionMenu = document.getElementById('section-menu');
                if (!sectionMenu) return;

                let menuContent = '';

                switch (sectionId) {
                    case 'overview':
                        menuContent = `
                            <h3><i class="fas fa-home"></i> Overview</h3>
                            <div style="padding: 1rem; background: #f8fafc; border-radius: 0.5rem; margin-top: 1rem;">
                                <p style="color: #64748b; font-size: 0.9rem;">Welcome to the 3D Brain Tumor Segmentation Platform!</p>
                                <p style="color: #64748b; font-size: 0.9rem; margin-top: 0.5rem;">Select a module from the navigation above to get started.</p>
                            </div>
                        `;
                        break;
                    case 'matlab-basics':
                        menuContent = `
                            <h3><i class="fas fa-code"></i> MATLAB Topics</h3>
                            <div style="padding: 1rem; background: #f8fafc; border-radius: 0.5rem; margin-top: 1rem;">
                                <p style="color: #64748b; font-size: 0.9rem;">Learn MATLAB programming fundamentals:</p>
                                <ul style="margin-top: 0.5rem; color: #64748b; font-size: 0.9rem;">
                                    <li>Variables & Operations</li>
                                    <li>Arrays & Matrices</li>
                                    <li>Functions & Plotting</li>
                                    <li>Image Processing</li>
                                </ul>
                            </div>
                        `;
                        break;
                    case 'dicom-processing':
                        menuContent = `
                            <h3><i class="fas fa-file-medical"></i> DICOM Tools</h3>
                            <div style="padding: 1rem; background: #f8fafc; border-radius: 0.5rem; margin-top: 1rem;">
                                <p style="color: #64748b; font-size: 0.9rem;">Medical image processing tools:</p>
                                <ul style="margin-top: 0.5rem; color: #64748b; font-size: 0.9rem;">
                                    <li>DICOM File Handling</li>
                                    <li>Image Viewing & Navigation</li>
                                    <li>Windowing & Leveling</li>
                                    <li>Image Analysis</li>
                                </ul>
                            </div>
                        `;
                        break;
                    case '3d-segmentation':
                        menuContent = `
                            <h3><i class="fas fa-cube"></i> 3D Tools</h3>
                            <div style="padding: 1rem; background: #f8fafc; border-radius: 0.5rem; margin-top: 1rem;">
                                <p style="color: #64748b; font-size: 0.9rem;">3D visualization and segmentation:</p>
                                <ul style="margin-top: 0.5rem; color: #64748b; font-size: 0.9rem;">
                                    <li>3D Brain Visualization</li>
                                    <li>Tumor Segmentation</li>
                                    <li>Volume Measurements</li>
                                    <li>Interactive Analysis</li>
                                </ul>
                            </div>
                        `;
                        break;
                    case 'exercises':
                        menuContent = `
                            <h3><i class="fas fa-tasks"></i> Exercise Categories</h3>
                            <div style="padding: 1rem; background: #f8fafc; border-radius: 0.5rem; margin-top: 1rem;">
                                <p style="color: #64748b; font-size: 0.9rem;">Practice with hands-on exercises:</p>
                                <ul style="margin-top: 0.5rem; color: #64748b; font-size: 0.9rem;">
                                    <li>MATLAB Fundamentals</li>
                                    <li>Image Processing</li>
                                    <li>3D Visualization</li>
                                    <li>Advanced Topics</li>
                                </ul>
                            </div>
                        `;
                        break;
                    default:
                        menuContent = '<p>Select a section to see available options.</p>';
                }

                sectionMenu.innerHTML = menuContent;
            }

            setupInteractions() {
                // MATLAB code editor buttons
                const runBtn = document.getElementById('run-matlab-code');
                if (runBtn) {
                    runBtn.addEventListener('click', () => this.runMATLABCode());
                }

                const clearBtn = document.getElementById('clear-matlab-code');
                if (clearBtn) {
                    clearBtn.addEventListener('click', () => this.clearMATLABCode());
                }

                const clearOutputBtn = document.getElementById('clear-output');
                if (clearOutputBtn) {
                    clearOutputBtn.addEventListener('click', () => this.clearMATLABOutput());
                }

                // Exercise items
                const exerciseItems = document.querySelectorAll('.exercise-item');
                exerciseItems.forEach(item => {
                    item.addEventListener('click', (e) => {
                        const exerciseId = e.currentTarget.dataset.exercise;
                        this.loadExercise(exerciseId);
                    });

                    item.addEventListener('mouseenter', (e) => {
                        e.currentTarget.style.borderColor = '#2563eb';
                        e.currentTarget.style.backgroundColor = '#f8fafc';
                    });

                    item.addEventListener('mouseleave', (e) => {
                        e.currentTarget.style.borderColor = '#e2e8f0';
                        e.currentTarget.style.backgroundColor = 'white';
                    });
                });

                // DICOM controls
                this.setupDICOMControls();
            }

            runMATLABCode() {
                const editor = document.getElementById('matlab-editor');
                const output = document.getElementById('matlab-output');

                if (!editor || !output) return;

                const code = editor.value.trim();
                if (!code) {
                    this.addMATLABOutput('No code to execute', 'warning');
                    return;
                }

                this.addMATLABOutput('>> Running MATLAB code...', 'info');

                try {
                    const result = this.simulateMATLAB(code);
                    if (result) {
                        this.addMATLABOutput(result, 'success');
                    }
                } catch (error) {
                    this.addMATLABOutput(`Error: ${error.message}`, 'error');
                }
            }

            simulateMATLAB(code) {
                const lines = code.split('\n').filter(line => {
                    const trimmed = line.trim();
                    return trimmed && !trimmed.startsWith('%');
                });

                let output = [];
                let variables = {};

                for (const line of lines) {
                    try {
                        const result = this.executeMATLABLine(line.trim(), variables);
                        if (result !== null && result !== undefined) {
                            output.push(result);
                        }
                    } catch (error) {
                        output.push(`Error in line "${line}": ${error.message}`);
                    }
                }

                return output.join('\n');
            }

            executeMATLABLine(line, variables) {
                // Handle variable assignments
                if (line.includes('=') && !line.includes('==')) {
                    const [varName, expression] = line.split('=').map(s => s.trim());
                    const cleanVarName = varName.replace(';', '');

                    let value;
                    if (expression.match(/^\d+$/)) {
                        value = parseInt(expression);
                    } else if (expression.match(/^\d+\.\d+$/)) {
                        value = parseFloat(expression);
                    } else if (expression.match(/^\[.*\]$/)) {
                        const arrayStr = expression.slice(1, -1);
                        value = arrayStr.split(',').map(s => parseFloat(s.trim()));
                    } else if (expression.includes('+') || expression.includes('-') || expression.includes('*') || expression.includes('/')) {
                        let expr = expression;
                        Object.keys(variables).forEach(varName => {
                            expr = expr.replace(new RegExp(`\\b${varName}\\b`, 'g'), variables[varName]);
                        });
                        try {
                            value = eval(expr);
                        } catch (e) {
                            value = expression;
                        }
                    } else {
                        value = expression;
                    }

                    variables[cleanVarName] = value;

                    if (!line.endsWith(';')) {
                        return `${cleanVarName} = ${Array.isArray(value) ? `[${value.join(', ')}]` : value}`;
                    }
                    return null;
                }

                // Handle disp function
                if (line.startsWith('disp(')) {
                    const content = line.slice(5, -1);
                    if (content.startsWith("'") && content.endsWith("'")) {
                        return content.slice(1, -1);
                    } else if (content.startsWith('[') && content.endsWith(']')) {
                        let result = content.slice(1, -1);
                        Object.keys(variables).forEach(varName => {
                            result = result.replace(new RegExp(`\\b${varName}\\b`, 'g'), variables[varName]);
                        });
                        result = result.replace(/'/g, '').replace(/,\s*/g, '');
                        return result;
                    } else if (variables[content]) {
                        return Array.isArray(variables[content]) ? `[${variables[content].join(', ')}]` : variables[content];
                    }
                    return content;
                }

                // Handle plot function
                if (line.startsWith('plot(')) {
                    return 'Plot created successfully (visualization not available in this demo)';
                }

                // Handle title function
                if (line.startsWith('title(')) {
                    const title = line.slice(6, -1).replace(/'/g, '');
                    return `Plot title set: ${title}`;
                }

                return null;
            }

            addMATLABOutput(text, type = 'normal') {
                const output = document.getElementById('matlab-output');
                if (!output) return;

                const outputLine = document.createElement('div');
                outputLine.className = `output-line ${type}`;
                outputLine.textContent = text;

                output.appendChild(outputLine);
                output.scrollTop = output.scrollHeight;
            }

            clearMATLABCode() {
                const editor = document.getElementById('matlab-editor');
                if (editor) editor.value = '';
            }

            clearMATLABOutput() {
                const output = document.getElementById('matlab-output');
                if (output) output.innerHTML = '';
            }

            setupDICOMControls() {
                const fileInput = document.getElementById('dicom-file-input');
                if (fileInput) {
                    fileInput.addEventListener('change', (e) => {
                        if (e.target.files.length > 0) {
                            this.showNotification(`Selected ${e.target.files.length} DICOM file(s)`, 'info');
                            this.simulateDICOMDisplay();
                        }
                    });
                }

                const sliders = ['brightness-slider', 'contrast-slider', 'window-level-slider', 'window-width-slider'];
                sliders.forEach(sliderId => {
                    const slider = document.getElementById(sliderId);
                    if (slider) {
                        slider.addEventListener('input', (e) => {
                            console.log(`${sliderId}: ${e.target.value}`);
                        });
                    }
                });
            }

            simulateDICOMDisplay() {
                const canvas = document.getElementById('dicom-canvas');
                if (!canvas) return;

                const ctx = canvas.getContext('2d');
                canvas.width = 512;
                canvas.height = 512;

                const imageData = ctx.createImageData(512, 512);
                const data = imageData.data;

                for (let i = 0; i < data.length; i += 4) {
                    const x = (i / 4) % 512;
                    const y = Math.floor((i / 4) / 512);
                    const centerX = 256;
                    const centerY = 256;
                    const distance = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);

                    let intensity = 0;
                    if (distance < 200) {
                        intensity = 150 + Math.sin(distance * 0.1) * 50;
                        intensity += (Math.random() - 0.5) * 30;
                    }

                    intensity = Math.max(0, Math.min(255, intensity));

                    data[i] = intensity;
                    data[i + 1] = intensity;
                    data[i + 2] = intensity;
                    data[i + 3] = 255;
                }

                ctx.putImageData(imageData, 0, 0);
                this.showNotification('Sample brain image displayed', 'success');
            }

            init3DViewer() {
                const container = document.getElementById('three-js-container');
                if (!container || !window.THREE) {
                    this.showNotification('Three.js not available', 'error');
                    return;
                }

                try {
                    container.innerHTML = '';

                    const scene = new THREE.Scene();
                    const camera = new THREE.PerspectiveCamera(75, container.clientWidth / container.clientHeight, 0.1, 1000);
                    const renderer = new THREE.WebGLRenderer();

                    renderer.setSize(container.clientWidth, container.clientHeight);
                    container.appendChild(renderer.domElement);

                    // Create brain
                    const geometry = new THREE.SphereGeometry(2, 32, 16);
                    const material = new THREE.MeshPhongMaterial({ color: 0xffc0cb, transparent: true, opacity: 0.8 });
                    const brain = new THREE.Mesh(geometry, material);
                    scene.add(brain);

                    // Create tumor
                    const tumorGeometry = new THREE.SphereGeometry(0.5, 16, 12);
                    const tumorMaterial = new THREE.MeshPhongMaterial({ color: 0xff4444, transparent: true, opacity: 0.9 });
                    const tumor = new THREE.Mesh(tumorGeometry, tumorMaterial);
                    tumor.position.set(1, 0.5, -0.5);
                    scene.add(tumor);

                    // Add lights
                    const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
                    scene.add(ambientLight);

                    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
                    directionalLight.position.set(10, 10, 5);
                    scene.add(directionalLight);

                    camera.position.z = 5;

                    const animate = () => {
                        requestAnimationFrame(animate);
                        brain.rotation.y += 0.01;
                        tumor.rotation.y += 0.01;
                        renderer.render(scene, camera);
                    };

                    animate();
                    this.showNotification('3D brain model loaded successfully!', 'success');

                } catch (error) {
                    console.error('3D viewer error:', error);
                    this.showNotification('Error initializing 3D viewer', 'error');
                }
            }

            showNotification(message, type = 'info') {
                const notification = document.createElement('div');
                notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    padding: 12px 20px;
                    border-radius: 6px;
                    color: white;
                    z-index: 1000;
                    max-width: 300px;
                    font-weight: 500;
                `;

                switch (type) {
                    case 'success':
                        notification.style.backgroundColor = '#10b981';
                        break;
                    case 'error':
                        notification.style.backgroundColor = '#ef4444';
                        break;
                    case 'warning':
                        notification.style.backgroundColor = '#f97316';
                        break;
                    default:
                        notification.style.backgroundColor = '#3b82f6';
                }

                notification.textContent = message;
                document.body.appendChild(notification);

                setTimeout(() => {
                    notification.remove();
                }, 3000);
            }
        }

        // Initialize the application when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            window.app = new SimpleBrainApp();
            console.log('🧠 3D Brain Tumor Segmentation Platform loaded successfully!');
        });
    </script>
</body>
</html>
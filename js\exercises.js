// Interactive Exercises Module

class ExerciseManager {
    constructor() {
        this.exercises = new Map();
        this.userProgress = new Map();
        this.currentExercise = null;
        
        this.initializeExercises();
        this.loadUserProgress();
        console.log('Exercise Manager initialized');
    }
    
    initializeExercises() {
        // MATLAB Fundamentals Exercises
        this.addExercise('matlab-1', {
            id: 'matlab-1',
            title: 'Variables and Arrays',
            category: 'matlab',
            difficulty: 'easy',
            description: 'Learn basic variable operations and array manipulation',
            instructions: `
                <h3>Variables and Arrays</h3>
                <p>Complete the following tasks in MATLAB:</p>
                <ol>
                    <li>Create a variable 'x' with value 10</li>
                    <li>Create a variable 'y' with value 5</li>
                    <li>Calculate the sum and store in 'result'</li>
                    <li>Create an array 'arr' with values [1, 2, 3, 4, 5]</li>
                    <li>Display the result using disp()</li>
                </ol>
            `,
            starterCode: `% Exercise 1: Variables and Arrays
% Complete the tasks below

% Task 1: Create variable x
x = ;

% Task 2: Create variable y
y = ;

% Task 3: Calculate sum
result = ;

% Task 4: Create array
arr = ;

% Task 5: Display result
disp();`,
            solution: `% Exercise 1: Variables and Arrays - Solution
x = 10;
y = 5;
result = x + y;
arr = [1, 2, 3, 4, 5];
disp(['Result: ', num2str(result)]);`,
            tests: [
                {
                    description: 'Variable x should equal 10',
                    test: (variables) => variables.has('x') && variables.get('x') === 10
                },
                {
                    description: 'Variable y should equal 5',
                    test: (variables) => variables.has('y') && variables.get('y') === 5
                },
                {
                    description: 'Result should equal 15',
                    test: (variables) => variables.has('result') && variables.get('result') === 15
                },
                {
                    description: 'Array should contain [1,2,3,4,5]',
                    test: (variables) => {
                        const arr = variables.get('arr');
                        return Array.isArray(arr) && arr.length === 5 && 
                               arr.every((val, idx) => val === idx + 1);
                    }
                }
            ],
            points: 10
        });
        
        this.addExercise('matlab-2', {
            id: 'matlab-2',
            title: 'Matrix Operations',
            category: 'matlab',
            difficulty: 'medium',
            description: 'Practice matrix multiplication and transformations',
            instructions: `
                <h3>Matrix Operations</h3>
                <p>Work with matrices in MATLAB:</p>
                <ol>
                    <li>Create a 2x3 matrix A with values [[1,2,3], [4,5,6]]</li>
                    <li>Create a 3x2 matrix B with values [[1,2], [3,4], [5,6]]</li>
                    <li>Multiply A and B to get matrix C</li>
                    <li>Find the transpose of C and store in C_transpose</li>
                </ol>
            `,
            starterCode: `% Exercise 2: Matrix Operations
% Complete the matrix operations below

% Task 1: Create matrix A (2x3)
A = ;

% Task 2: Create matrix B (3x2)
B = ;

% Task 3: Multiply A and B
C = ;

% Task 4: Transpose of C
C_transpose = ;

% Display results
disp('Matrix A:');
disp(A);
disp('Matrix B:');
disp(B);
disp('Matrix C (A*B):');
disp(C);`,
            solution: `% Exercise 2: Matrix Operations - Solution
A = [1, 2, 3; 4, 5, 6];
B = [1, 2; 3, 4; 5, 6];
C = A * B;
C_transpose = C';

disp('Matrix A:');
disp(A);
disp('Matrix B:');
disp(B);
disp('Matrix C (A*B):');
disp(C);`,
            tests: [
                {
                    description: 'Matrix A should be 2x3',
                    test: (variables) => {
                        const A = variables.get('A');
                        return Array.isArray(A) && A.length === 2 && A[0].length === 3;
                    }
                },
                {
                    description: 'Matrix B should be 3x2',
                    test: (variables) => {
                        const B = variables.get('B');
                        return Array.isArray(B) && B.length === 3 && B[0].length === 2;
                    }
                },
                {
                    description: 'Matrix C should be the product of A and B',
                    test: (variables) => {
                        const C = variables.get('C');
                        return Array.isArray(C) && C.length === 2 && C[0].length === 2;
                    }
                }
            ],
            points: 15
        });
        
        // Image Processing Exercises
        this.addExercise('image-1', {
            id: 'image-1',
            title: 'Image Filtering',
            category: 'image',
            difficulty: 'medium',
            description: 'Apply filters to medical images',
            instructions: `
                <h3>Image Filtering</h3>
                <p>Learn basic image filtering techniques:</p>
                <ol>
                    <li>Load a sample medical image</li>
                    <li>Apply a Gaussian filter for noise reduction</li>
                    <li>Apply edge detection using Sobel filter</li>
                    <li>Compare original and filtered images</li>
                </ol>
            `,
            starterCode: `% Exercise: Image Filtering
% Load and process medical images

% Task 1: Create sample image (simulated brain scan)
image = rand(256, 256) * 255;

% Task 2: Apply Gaussian filter (simplified)
% filtered_image = ;

% Task 3: Apply edge detection
% edges = ;

% Display results
figure;
subplot(1,3,1);
imshow(image, []);
title('Original');

% Add your filtered image displays here`,
            solution: `% Image Filtering - Solution
image = rand(256, 256) * 255;

% Gaussian filter (simplified)
h = fspecial('gaussian', [5 5], 1);
filtered_image = imfilter(image, h);

% Edge detection
edges = edge(image, 'sobel');

figure;
subplot(1,3,1);
imshow(image, []);
title('Original');

subplot(1,3,2);
imshow(filtered_image, []);
title('Gaussian Filtered');

subplot(1,3,3);
imshow(edges);
title('Edge Detection');`,
            tests: [
                {
                    description: 'Image should be created',
                    test: (variables) => variables.has('image')
                }
            ],
            points: 20
        });
        
        this.addExercise('image-2', {
            id: 'image-2',
            title: 'Tumor Detection',
            category: 'image',
            difficulty: 'hard',
            description: 'Implement basic tumor detection algorithms',
            instructions: `
                <h3>Tumor Detection</h3>
                <p>Implement a basic tumor detection algorithm:</p>
                <ol>
                    <li>Load brain MRI image</li>
                    <li>Apply preprocessing (noise reduction, contrast enhancement)</li>
                    <li>Segment potential tumor regions using thresholding</li>
                    <li>Calculate tumor area and characteristics</li>
                </ol>
            `,
            starterCode: `% Exercise: Tumor Detection
% Implement basic tumor detection

% Task 1: Create simulated brain image with tumor
brain_image = create_brain_image();

% Task 2: Preprocessing
% preprocessed = ;

% Task 3: Tumor segmentation
% tumor_mask = ;

% Task 4: Calculate tumor area
% tumor_area = ;

function img = create_brain_image()
    % Create simulated brain image
    img = zeros(256, 256);
    % Add brain tissue
    [X, Y] = meshgrid(1:256, 1:256);
    center = [128, 128];
    brain_mask = sqrt((X-center(1)).^2 + (Y-center(2)).^2) < 100;
    img(brain_mask) = 100 + 50*rand(sum(brain_mask(:)), 1);
    
    % Add tumor
    tumor_center = [150, 120];
    tumor_mask = sqrt((X-tumor_center(1)).^2 + (Y-tumor_center(2)).^2) < 20;
    img(tumor_mask) = 200 + 30*rand(sum(tumor_mask(:)), 1);
end`,
            solution: `% Tumor Detection - Solution
brain_image = create_brain_image();

% Preprocessing
h = fspecial('gaussian', [5 5], 1);
preprocessed = imfilter(brain_image, h);

% Tumor segmentation using thresholding
threshold = 180;
tumor_mask = preprocessed > threshold;

% Calculate tumor area
tumor_area = sum(tumor_mask(:));

% Display results
figure;
subplot(2,2,1);
imshow(brain_image, []);
title('Original Brain Image');

subplot(2,2,2);
imshow(preprocessed, []);
title('Preprocessed');

subplot(2,2,3);
imshow(tumor_mask);
title('Tumor Mask');

subplot(2,2,4);
imshow(brain_image + 100*tumor_mask, []);
title(['Detected Tumor (Area: ' num2str(tumor_area) ')']);

function img = create_brain_image()
    img = zeros(256, 256);
    [X, Y] = meshgrid(1:256, 1:256);
    center = [128, 128];
    brain_mask = sqrt((X-center(1)).^2 + (Y-center(2)).^2) < 100;
    img(brain_mask) = 100 + 50*rand(sum(brain_mask(:)), 1);
    
    tumor_center = [150, 120];
    tumor_mask = sqrt((X-tumor_center(1)).^2 + (Y-tumor_center(2)).^2) < 20;
    img(tumor_mask) = 200 + 30*rand(sum(tumor_mask(:)), 1);
end`,
            tests: [
                {
                    description: 'Brain image should be created',
                    test: (variables) => variables.has('brain_image')
                },
                {
                    description: 'Tumor area should be calculated',
                    test: (variables) => variables.has('tumor_area') && variables.get('tumor_area') > 0
                }
            ],
            points: 30
        });
    }
    
    addExercise(id, exercise) {
        this.exercises.set(id, exercise);
    }
    
    getExercise(id) {
        return this.exercises.get(id);
    }
    
    getAllExercises() {
        return Array.from(this.exercises.values());
    }
    
    getExercisesByCategory(category) {
        return this.getAllExercises().filter(ex => ex.category === category);
    }
    
    getExercisesByDifficulty(difficulty) {
        return this.getAllExercises().filter(ex => ex.difficulty === difficulty);
    }
    
    loadExercise(exerciseId) {
        const exercise = this.getExercise(exerciseId);
        if (!exercise) {
            console.error(`Exercise ${exerciseId} not found`);
            return;
        }
        
        this.currentExercise = exercise;
        this.displayExercise(exercise);
        
        // Track exercise start
        this.trackExerciseStart(exerciseId);
    }
    
    displayExercise(exercise) {
        const exerciseContent = document.getElementById('exercise-content');
        if (!exerciseContent) return;
        
        exerciseContent.innerHTML = `
            <div class="exercise-header">
                <h2>${exercise.title}</h2>
                <div class="exercise-meta">
                    <span class="difficulty ${exercise.difficulty}">${exercise.difficulty.toUpperCase()}</span>
                    <span class="points">${exercise.points} points</span>
                </div>
            </div>
            
            <div class="exercise-instructions">
                ${exercise.instructions}
            </div>
            
            <div class="exercise-workspace">
                <div class="code-section">
                    <div class="code-header">
                        <h4>Your Code</h4>
                        <div class="code-actions">
                            <button class="btn-load-starter" onclick="window.exerciseManager.loadStarterCode()">
                                <i class="fas fa-undo"></i> Reset
                            </button>
                            <button class="btn-run-exercise" onclick="window.exerciseManager.runExercise()">
                                <i class="fas fa-play"></i> Run
                            </button>
                            <button class="btn-check-solution" onclick="window.exerciseManager.checkSolution()">
                                <i class="fas fa-check"></i> Check
                            </button>
                        </div>
                    </div>
                    <textarea id="exercise-code-editor" class="exercise-code-editor">${exercise.starterCode}</textarea>
                </div>
                
                <div class="output-section">
                    <div class="output-header">
                        <h4>Output</h4>
                        <button class="btn-clear-output" onclick="window.exerciseManager.clearOutput()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div id="exercise-output" class="exercise-output"></div>
                </div>
                
                <div class="tests-section">
                    <div class="tests-header">
                        <h4>Tests</h4>
                        <span class="test-status" id="test-status">Not run</span>
                    </div>
                    <div id="test-results" class="test-results"></div>
                </div>
            </div>
            
            <div class="exercise-actions">
                <button class="btn-hint" onclick="window.exerciseManager.showHint()">
                    <i class="fas fa-lightbulb"></i> Hint
                </button>
                <button class="btn-solution" onclick="window.exerciseManager.showSolution()">
                    <i class="fas fa-eye"></i> Show Solution
                </button>
                <button class="btn-submit" onclick="window.exerciseManager.submitExercise()" disabled>
                    <i class="fas fa-paper-plane"></i> Submit
                </button>
            </div>
        `;
        
        // Initialize code editor if CodeMirror is available
        this.initializeExerciseEditor();
    }
    
    initializeExerciseEditor() {
        const editorElement = document.getElementById('exercise-code-editor');
        if (!editorElement) return;
        
        if (typeof CodeMirror !== 'undefined') {
            this.exerciseEditor = CodeMirror.fromTextArea(editorElement, {
                mode: 'octave',
                theme: 'monokai',
                lineNumbers: true,
                autoCloseBrackets: true,
                matchBrackets: true,
                indentUnit: 4,
                tabSize: 4,
                lineWrapping: true
            });
        }
    }
    
    loadStarterCode() {
        if (!this.currentExercise) return;
        
        if (this.exerciseEditor) {
            this.exerciseEditor.setValue(this.currentExercise.starterCode);
        } else {
            const editor = document.getElementById('exercise-code-editor');
            if (editor) editor.value = this.currentExercise.starterCode;
        }
        
        this.clearOutput();
        this.clearTestResults();
    }
    
    runExercise() {
        if (!this.currentExercise) return;
        
        const code = this.getEditorCode();
        if (!code.trim()) {
            this.addOutput('No code to run', 'warning');
            return;
        }
        
        try {
            // Use the MATLAB simulator to run the code
            if (window.app && window.app.matlabSimulator) {
                const result = window.app.matlabSimulator.executeCode(code);
                if (result) {
                    this.addOutput(result, 'success');
                }
            } else {
                this.addOutput('MATLAB simulator not available', 'error');
            }
        } catch (error) {
            this.addOutput(`Error: ${error.message}`, 'error');
        }
    }
    
    checkSolution() {
        if (!this.currentExercise) return;
        
        const code = this.getEditorCode();
        const testResults = this.runTests(code);
        
        this.displayTestResults(testResults);
        
        const allPassed = testResults.every(result => result.passed);
        const submitBtn = document.querySelector('.btn-submit');
        
        if (submitBtn) {
            submitBtn.disabled = !allPassed;
        }
        
        if (allPassed) {
            this.addOutput('All tests passed! You can now submit your solution.', 'success');
        } else {
            this.addOutput('Some tests failed. Please review your code.', 'warning');
        }
    }
    
    runTests(code) {
        if (!this.currentExercise || !this.currentExercise.tests) return [];
        
        const results = [];
        
        try {
            // Extract variables from code (simplified)
            const variables = this.extractVariables(code);
            
            for (const test of this.currentExercise.tests) {
                try {
                    const passed = test.test(variables);
                    results.push({
                        description: test.description,
                        passed: passed,
                        error: null
                    });
                } catch (error) {
                    results.push({
                        description: test.description,
                        passed: false,
                        error: error.message
                    });
                }
            }
        } catch (error) {
            console.error('Error running tests:', error);
        }
        
        return results;
    }
    
    extractVariables(code) {
        // Simplified variable extraction
        const variables = new Map();
        const lines = code.split('\n');
        
        for (const line of lines) {
            const trimmed = line.trim();
            if (trimmed && !trimmed.startsWith('%') && trimmed.includes('=')) {
                try {
                    const [varName, expression] = trimmed.split('=').map(s => s.trim());
                    if (varName && !varName.includes('(')) {
                        // Simple evaluation (in real implementation, use MATLAB simulator)
                        const cleanVarName = varName.replace(';', '');
                        
                        // Handle simple cases
                        if (expression.match(/^\d+$/)) {
                            variables.set(cleanVarName, parseInt(expression));
                        } else if (expression.match(/^\d+\.\d+$/)) {
                            variables.set(cleanVarName, parseFloat(expression));
                        } else if (expression.match(/^\[.*\]$/)) {
                            // Simple array parsing
                            const arrayStr = expression.slice(1, -1);
                            const values = arrayStr.split(',').map(s => parseFloat(s.trim()));
                            variables.set(cleanVarName, values);
                        }
                    }
                } catch (error) {
                    // Skip problematic lines
                }
            }
        }
        
        return variables;
    }
    
    displayTestResults(results) {
        const testResultsEl = document.getElementById('test-results');
        const testStatusEl = document.getElementById('test-status');
        
        if (!testResultsEl || !testStatusEl) return;
        
        const passedCount = results.filter(r => r.passed).length;
        const totalCount = results.length;
        
        testStatusEl.textContent = `${passedCount}/${totalCount} passed`;
        testStatusEl.className = `test-status ${passedCount === totalCount ? 'all-passed' : 'some-failed'}`;
        
        testResultsEl.innerHTML = results.map(result => `
            <div class="test-result ${result.passed ? 'passed' : 'failed'}">
                <i class="fas ${result.passed ? 'fa-check' : 'fa-times'}"></i>
                <span class="test-description">${result.description}</span>
                ${result.error ? `<span class="test-error">${result.error}</span>` : ''}
            </div>
        `).join('');
    }
    
    showHint() {
        if (!this.currentExercise) return;
        
        // Generate contextual hints based on the exercise
        const hints = this.generateHints(this.currentExercise);
        
        if (hints.length > 0) {
            const hint = hints[Math.floor(Math.random() * hints.length)];
            this.addOutput(`💡 Hint: ${hint}`, 'info');
        } else {
            this.addOutput('💡 Hint: Review the exercise instructions and try breaking down the problem into smaller steps.', 'info');
        }
    }
    
    generateHints(exercise) {
        const hints = [];
        
        switch (exercise.category) {
            case 'matlab':
                hints.push('Remember to use semicolons to suppress output when needed');
                hints.push('MATLAB arrays are created using square brackets []');
                hints.push('Use the disp() function to display results');
                break;
            case 'image':
                hints.push('Image processing often involves filtering and thresholding');
                hints.push('Use imshow() to display images');
                hints.push('Consider the image data type and range');
                break;
        }
        
        return hints;
    }
    
    showSolution() {
        if (!this.currentExercise) return;
        
        if (this.exerciseEditor) {
            this.exerciseEditor.setValue(this.currentExercise.solution);
        } else {
            const editor = document.getElementById('exercise-code-editor');
            if (editor) editor.value = this.currentExercise.solution;
        }
        
        this.addOutput('Solution loaded. Study it carefully!', 'info');
    }
    
    submitExercise() {
        if (!this.currentExercise) return;
        
        const code = this.getEditorCode();
        const testResults = this.runTests(code);
        const allPassed = testResults.every(result => result.passed);
        
        if (!allPassed) {
            this.addOutput('Please ensure all tests pass before submitting.', 'warning');
            return;
        }
        
        // Record completion
        this.recordCompletion(this.currentExercise.id, {
            code: code,
            points: this.currentExercise.points,
            completedAt: new Date().toISOString()
        });
        
        this.addOutput(`🎉 Exercise completed! You earned ${this.currentExercise.points} points.`, 'success');
        
        // Update UI
        this.updateExerciseStatus(this.currentExercise.id, 'completed');
        
        // Update global progress
        if (window.app) {
            window.app.userProgress.completedExercises++;
            window.app.userProgress.totalScore += this.currentExercise.points;
            window.app.updateProgressDisplay();
        }
    }
    
    getEditorCode() {
        if (this.exerciseEditor) {
            return this.exerciseEditor.getValue();
        } else {
            const editor = document.getElementById('exercise-code-editor');
            return editor ? editor.value : '';
        }
    }
    
    addOutput(text, type = 'normal') {
        const outputEl = document.getElementById('exercise-output');
        if (!outputEl) return;
        
        const outputLine = document.createElement('div');
        outputLine.className = `output-line ${type}`;
        outputLine.textContent = text;
        
        outputEl.appendChild(outputLine);
        outputEl.scrollTop = outputEl.scrollHeight;
    }
    
    clearOutput() {
        const outputEl = document.getElementById('exercise-output');
        if (outputEl) outputEl.innerHTML = '';
    }
    
    clearTestResults() {
        const testResultsEl = document.getElementById('test-results');
        const testStatusEl = document.getElementById('test-status');
        
        if (testResultsEl) testResultsEl.innerHTML = '';
        if (testStatusEl) {
            testStatusEl.textContent = 'Not run';
            testStatusEl.className = 'test-status';
        }
    }
    
    recordCompletion(exerciseId, data) {
        this.userProgress.set(exerciseId, data);
        this.saveUserProgress();
    }
    
    updateExerciseStatus(exerciseId, status) {
        const exerciseItem = document.querySelector(`[data-exercise="${exerciseId}"]`);
        if (exerciseItem) {
            const statusIcon = exerciseItem.querySelector('.exercise-status i');
            if (statusIcon) {
                statusIcon.className = status === 'completed' ? 'fas fa-check-circle completed' : 'fas fa-circle';
            }
        }
    }
    
    saveUserProgress() {
        const progressData = Object.fromEntries(this.userProgress);
        localStorage.setItem('exerciseProgress', JSON.stringify(progressData));
    }
    
    loadUserProgress() {
        const saved = localStorage.getItem('exerciseProgress');
        if (saved) {
            const progressData = JSON.parse(saved);
            this.userProgress = new Map(Object.entries(progressData));
            
            // Update UI for completed exercises
            for (const [exerciseId] of this.userProgress) {
                this.updateExerciseStatus(exerciseId, 'completed');
            }
        }
    }
    
    trackExerciseStart(exerciseId) {
        // Track when user starts an exercise
        console.log(`Started exercise: ${exerciseId}`);
    }
}

// Make class available globally
window.ExerciseManager = ExerciseManager;

// Create global instance when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    if (!window.exerciseManager) {
        window.exerciseManager = new ExerciseManager();
    }
});

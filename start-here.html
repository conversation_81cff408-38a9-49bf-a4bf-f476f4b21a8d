<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Start Here - 3D Brain Tumor Segmentation Platform</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: #1e293b;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2563eb, #10b981);
            color: white;
            padding: 40px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px;
        }
        
        .status-check {
            background: #f0fdf4;
            border: 1px solid #22c55e;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: center;
            color: #166534;
        }
        
        .quick-start {
            background: #f8fafc;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
        }
        
        .quick-start h3 {
            color: #1e293b;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .step {
            display: flex;
            align-items: flex-start;
            gap: 15px;
            margin-bottom: 20px;
            padding: 15px;
            background: white;
            border-radius: 10px;
            border: 1px solid #e2e8f0;
        }
        
        .step-number {
            background: #2563eb;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            flex-shrink: 0;
        }
        
        .step-content h4 {
            color: #1e293b;
            margin-bottom: 5px;
        }
        
        .step-content p {
            color: #64748b;
            font-size: 0.95rem;
        }
        
        .action-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .btn {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            padding: 15px 20px;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            text-align: center;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #2563eb, #10b981);
            color: white;
            box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(37, 99, 235, 0.4);
        }
        
        .btn-secondary {
            background: white;
            color: #2563eb;
            border: 2px solid #2563eb;
        }
        
        .btn-secondary:hover {
            background: #2563eb;
            color: white;
            transform: translateY(-2px);
        }
        
        .features-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .feature {
            background: #f8fafc;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #e2e8f0;
        }
        
        .feature h4 {
            color: #1e293b;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .feature p {
            color: #64748b;
            font-size: 0.9rem;
        }
        
        .troubleshooting {
            background: #fef3c7;
            border: 1px solid #fbbf24;
            border-radius: 10px;
            padding: 20px;
            margin: 30px 0;
        }
        
        .troubleshooting h4 {
            color: #92400e;
            margin-bottom: 15px;
        }
        
        .troubleshooting ul {
            color: #92400e;
            padding-left: 20px;
        }
        
        .troubleshooting li {
            margin-bottom: 8px;
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .content {
                padding: 20px;
            }
            
            .action-buttons {
                grid-template-columns: 1fr;
            }
            
            .features-list {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-brain"></i> Welcome!</h1>
            <p>3D Brain Tumor Segmentation Platform</p>
        </div>
        
        <div class="content">
            <div id="status" class="status-check">
                <i class="fas fa-check-circle"></i>
                <strong>Platform Ready!</strong> All files are properly configured and ready to use.
            </div>
            
            <div class="quick-start">
                <h3><i class="fas fa-rocket"></i> Quick Start Guide</h3>
                
                <div class="step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <h4>Launch the Platform</h4>
                        <p>Click the "Launch Platform" button below to start the interactive learning experience.</p>
                    </div>
                </div>
                
                <div class="step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <h4>Explore the Modules</h4>
                        <p>Navigate through MATLAB Basics, DICOM Processing, 3D Segmentation, and Interactive Exercises.</p>
                    </div>
                </div>
                
                <div class="step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <h4>Start Learning</h4>
                        <p>Begin with the Overview section, then progress through the modules at your own pace.</p>
                    </div>
                </div>
                
                <div class="step">
                    <div class="step-number">4</div>
                    <div class="step-content">
                        <h4>Practice with Exercises</h4>
                        <p>Test your knowledge with interactive coding exercises and real-time feedback.</p>
                    </div>
                </div>
            </div>
            
            <div class="action-buttons">
                <a href="platform.html" class="btn btn-primary pulse">
                    <i class="fas fa-rocket"></i> Launch Platform
                </a>
                <a href="index.html" class="btn btn-secondary">
                    <i class="fas fa-home"></i> Main Page
                </a>
                <a href="demo.html" class="btn btn-secondary">
                    <i class="fas fa-eye"></i> View Demo
                </a>
                <a href="test.html" class="btn btn-secondary">
                    <i class="fas fa-cog"></i> Run Tests
                </a>
            </div>
            
            <div class="features-list">
                <div class="feature">
                    <h4><i class="fas fa-code" style="color: #10b981;"></i> MATLAB Programming</h4>
                    <p>Interactive code editor with real-time execution and comprehensive tutorials</p>
                </div>
                
                <div class="feature">
                    <h4><i class="fas fa-file-medical" style="color: #2563eb;"></i> DICOM Processing</h4>
                    <p>Medical image handling, visualization, and advanced processing techniques</p>
                </div>
                
                <div class="feature">
                    <h4><i class="fas fa-cube" style="color: #f59e0b;"></i> 3D Visualization</h4>
                    <p>Interactive Three.js-based brain models with tumor segmentation</p>
                </div>
                
                <div class="feature">
                    <h4><i class="fas fa-tasks" style="color: #ef4444;"></i> Interactive Exercises</h4>
                    <p>Progressive coding challenges with automated testing and feedback</p>
                </div>
            </div>
            
            <div class="troubleshooting">
                <h4><i class="fas fa-exclamation-triangle"></i> Troubleshooting</h4>
                <ul>
                    <li><strong>If platform doesn't load:</strong> Make sure you're using a modern browser (Chrome, Firefox, Safari, Edge)</li>
                    <li><strong>For local development:</strong> Run from a web server, not file:// protocol</li>
                    <li><strong>3D viewer issues:</strong> Ensure WebGL is enabled in your browser</li>
                    <li><strong>Performance issues:</strong> Close other browser tabs and applications</li>
                </ul>
            </div>
            
            <div style="text-align: center; margin-top: 30px; padding: 20px; background: #e0f2fe; border-radius: 10px;">
                <h4 style="color: #0369a1; margin-bottom: 10px;">🎓 Educational Platform</h4>
                <p style="color: #0369a1; margin: 0;">Perfect for medical students, researchers, and healthcare professionals learning medical image processing and MATLAB programming.</p>
            </div>
        </div>
    </div>

    <script>
        // Check platform status
        function checkStatus() {
            const status = document.getElementById('status');
            
            fetch('platform.html', { method: 'HEAD' })
                .then(response => {
                    if (response.ok) {
                        status.className = 'status-check';
                        status.style.background = '#f0fdf4';
                        status.style.borderColor = '#22c55e';
                        status.style.color = '#166534';
                        status.innerHTML = `
                            <i class="fas fa-check-circle"></i>
                            <strong>Platform Ready!</strong> All files are properly configured and ready to use.
                        `;
                    } else {
                        throw new Error('Platform not accessible');
                    }
                })
                .catch(error => {
                    status.style.background = '#fef3c7';
                    status.style.borderColor = '#fbbf24';
                    status.style.color = '#92400e';
                    status.innerHTML = `
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Setup Required:</strong> Please run from a web server for full functionality.
                    `;
                });
        }
        
        // Add button click effects
        document.addEventListener('click', (e) => {
            if (e.target.matches('.btn')) {
                e.target.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    e.target.style.transform = '';
                }, 150);
            }
        });
        
        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            checkStatus();
        });
    </script>
</body>
</html>

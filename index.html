<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Brain Tumor Segmentation - Interactive Learning Platform</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: #1e293b;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            padding: 60px 0;
        }
        
        .header h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.25rem;
            opacity: 0.9;
            margin-bottom: 40px;
        }
        
        .main-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 40px;
        }
        
        .card-header {
            background: linear-gradient(135deg, #2563eb, #10b981);
            color: white;
            padding: 40px;
            text-align: center;
        }
        
        .card-content {
            padding: 40px;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .feature-item {
            text-align: center;
            padding: 30px 20px;
            border-radius: 15px;
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            transition: all 0.3s ease;
        }
        
        .feature-item:hover {
            transform: translateY(-5px);
            border-color: #2563eb;
            box-shadow: 0 10px 25px rgba(37, 99, 235, 0.1);
        }
        
        .feature-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #2563eb, #10b981);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 1.5rem;
            color: white;
        }
        
        .feature-item h3 {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 10px;
            color: #1e293b;
        }
        
        .feature-item p {
            color: #64748b;
            font-size: 0.95rem;
            line-height: 1.5;
        }
        
        .action-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
            margin: 40px 0;
        }
        
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 10px;
            padding: 15px 30px;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #2563eb, #10b981);
            color: white;
            box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(37, 99, 235, 0.4);
        }
        
        .btn-secondary {
            background: white;
            color: #2563eb;
            border: 2px solid #2563eb;
        }
        
        .btn-secondary:hover {
            background: #2563eb;
            color: white;
            transform: translateY(-2px);
        }
        
        .status-card {
            background: #f0fdf4;
            border: 1px solid #22c55e;
            border-radius: 10px;
            padding: 20px;
            margin: 30px 0;
            text-align: center;
            color: #166534;
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .header p {
                font-size: 1rem;
            }
            
            .card-content {
                padding: 20px;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .action-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 100%;
                max-width: 300px;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-brain"></i> 3D Brain Tumor Segmentation</h1>
            <p>Interactive Learning Platform for Medical Image Processing & MATLAB Programming</p>
        </div>
        
        <div class="main-card">
            <div class="card-header">
                <h2>Welcome to Advanced Medical Imaging Education</h2>
                <p>Learn MATLAB programming, DICOM processing, and 3D visualization through hands-on interactive experiences</p>
            </div>
            
            <div class="card-content">
                <div class="status-card">
                    <i class="fas fa-check-circle"></i>
                    <strong>Platform Ready!</strong> All systems operational and ready for learning.
                </div>
                
                <div class="features-grid">
                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="fas fa-code"></i>
                        </div>
                        <h3>MATLAB Programming</h3>
                        <p>Interactive code editor with real-time execution, comprehensive tutorials, and hands-on exercises</p>
                    </div>
                    
                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="fas fa-file-medical"></i>
                        </div>
                        <h3>DICOM Processing</h3>
                        <p>Medical image handling, visualization, windowing, and advanced processing techniques</p>
                    </div>
                    
                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="fas fa-cube"></i>
                        </div>
                        <h3>3D Visualization</h3>
                        <p>Interactive Three.js-based brain models with tumor segmentation and volume analysis</p>
                    </div>
                    
                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="fas fa-tasks"></i>
                        </div>
                        <h3>Interactive Exercises</h3>
                        <p>Progressive coding challenges with automated testing, hints, and instant feedback</p>
                    </div>
                </div>
                
                <div class="action-buttons">
                    <a href="platform.html" class="btn btn-primary pulse">
                        <i class="fas fa-rocket"></i> Launch Platform
                    </a>
                    <a href="enhanced-realtime-3d.html" class="btn btn-secondary" style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; border: none;">
                        <i class="fas fa-magic"></i> Enhanced 3D
                    </a>
                    <a href="ml-dl-methods.html" class="btn btn-secondary" style="background: linear-gradient(135deg, #f093fb, #f5576c); color: white; border: none;">
                        <i class="fas fa-brain"></i> ML & DL Methods
                    </a>
                    <a href="mri-processing-gui.html" class="btn btn-secondary" style="background: linear-gradient(135deg, #11998e, #38ef7d); color: white; border: none;">
                        <i class="fas fa-upload"></i> MRI Processing GUI
                    </a>
                    <a href="realtime-3d-segmentation.html" class="btn btn-secondary">
                        <i class="fas fa-cube"></i> Real-time 3D
                    </a>
                    <a href="segmentation-methods.html" class="btn btn-secondary">
                        <i class="fas fa-microscope"></i> Methods Guide
                    </a>
                </div>
                
                <div style="background: #e0f2fe; padding: 20px; border-radius: 10px; margin: 20px 0;">
                    <h4 style="color: #0369a1; margin-bottom: 15px;">🎓 Perfect for:</h4>
                    <ul style="color: #0369a1; line-height: 1.8; list-style: none; padding: 0;">
                        <li><i class="fas fa-check" style="color: #10b981; margin-right: 10px;"></i> Medical students learning radiology</li>
                        <li><i class="fas fa-check" style="color: #10b981; margin-right: 10px;"></i> Engineering students in biomedical programs</li>
                        <li><i class="fas fa-check" style="color: #10b981; margin-right: 10px;"></i> Researchers new to medical imaging</li>
                        <li><i class="fas fa-check" style="color: #10b981; margin-right: 10px;"></i> Healthcare professionals learning programming</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div style="text-align: center; color: white; padding: 40px 0; opacity: 0.9;">
            <p>🌐 Works in any modern browser - no installation required!</p>
            <p style="margin-top: 10px; font-size: 0.9rem;">
                Built with ❤️ for medical education and research
            </p>
        </div>
    </div>

    <script>
        // Add interactive effects
        document.addEventListener('DOMContentLoaded', () => {
            // Button click effects
            document.addEventListener('click', (e) => {
                if (e.target.matches('.btn')) {
                    e.target.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        e.target.style.transform = '';
                    }, 150);
                }
            });
            
            // Feature card animations
            const featureItems = document.querySelectorAll('.feature-item');
            featureItems.forEach((item, index) => {
                item.style.opacity = '0';
                item.style.transform = 'translateY(20px)';
                item.style.transition = 'all 0.6s ease';
                
                setTimeout(() => {
                    item.style.opacity = '1';
                    item.style.transform = 'translateY(0)';
                }, 100 + index * 100);
            });
        });
    </script>
</body>
</html>

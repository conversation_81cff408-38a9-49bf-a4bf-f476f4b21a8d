// 3D Brain Tumor Segmentation Viewer using Three.js

class BrainViewer3D {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.controls = null;
        this.brainMesh = null;
        this.tumorMesh = null;
        this.annotations = [];
        this.measurements = [];
        this.isInitialized = false;
        
        // Animation properties
        this.animationId = null;
        this.rotationSpeed = 0.01;
        this.autoRotate = true;
        
        // Segmentation properties
        this.segmentationMode = 'view';
        this.selectedTool = 'view';
        
        this.init();
    }
    
    init() {
        if (!this.container) {
            console.error('3D container not found');
            return;
        }
        
        this.setupScene();
        this.setupCamera();
        this.setupRenderer();
        this.setupControls();
        this.setupLights();
        this.createBrainModel();
        this.createTumorModel();
        this.setupEventListeners();
        this.animate();
        
        this.isInitialized = true;
        console.log('3D Brain Viewer initialized successfully');
    }
    
    setupScene() {
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x1e293b);
        
        // Add fog for depth perception
        this.scene.fog = new THREE.Fog(0x1e293b, 10, 50);
    }
    
    setupCamera() {
        const width = this.container.clientWidth;
        const height = this.container.clientHeight;
        
        this.camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000);
        this.camera.position.set(0, 0, 15);
        this.camera.lookAt(0, 0, 0);
    }
    
    setupRenderer() {
        this.renderer = new THREE.WebGLRenderer({ 
            antialias: true,
            alpha: true,
            powerPreference: "high-performance"
        });
        
        this.renderer.setSize(this.container.clientWidth, this.container.clientHeight);
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        this.renderer.outputEncoding = THREE.sRGBEncoding;
        this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
        this.renderer.toneMappingExposure = 1.2;
        
        this.container.appendChild(this.renderer.domElement);
    }
    
    setupControls() {
        // Note: In a real implementation, you'd import OrbitControls
        // For this demo, we'll implement basic mouse controls
        this.setupBasicControls();
    }
    
    setupBasicControls() {
        let isMouseDown = false;
        let mouseX = 0;
        let mouseY = 0;
        let targetRotationX = 0;
        let targetRotationY = 0;
        
        this.renderer.domElement.addEventListener('mousedown', (event) => {
            isMouseDown = true;
            mouseX = event.clientX;
            mouseY = event.clientY;
        });
        
        this.renderer.domElement.addEventListener('mousemove', (event) => {
            if (!isMouseDown) return;
            
            const deltaX = event.clientX - mouseX;
            const deltaY = event.clientY - mouseY;
            
            targetRotationY += deltaX * 0.01;
            targetRotationX += deltaY * 0.01;
            
            mouseX = event.clientX;
            mouseY = event.clientY;
        });
        
        this.renderer.domElement.addEventListener('mouseup', () => {
            isMouseDown = false;
        });
        
        this.renderer.domElement.addEventListener('wheel', (event) => {
            const delta = event.deltaY * 0.001;
            this.camera.position.z += delta;
            this.camera.position.z = Math.max(5, Math.min(30, this.camera.position.z));
        });
        
        // Apply rotation in animation loop
        this.updateRotation = () => {
            if (this.scene.children.length > 0) {
                const brainGroup = this.scene.children.find(child => child.userData.type === 'brainGroup');
                if (brainGroup) {
                    brainGroup.rotation.x += (targetRotationX - brainGroup.rotation.x) * 0.1;
                    brainGroup.rotation.y += (targetRotationY - brainGroup.rotation.y) * 0.1;
                }
            }
        };
    }
    
    setupLights() {
        // Ambient light
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);
        
        // Directional light
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(10, 10, 5);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        this.scene.add(directionalLight);
        
        // Point lights for better illumination
        const pointLight1 = new THREE.PointLight(0x4fc3f7, 0.5, 50);
        pointLight1.position.set(-10, 0, 10);
        this.scene.add(pointLight1);
        
        const pointLight2 = new THREE.PointLight(0xff7043, 0.3, 50);
        pointLight2.position.set(10, -10, -10);
        this.scene.add(pointLight2);
    }
    
    createBrainModel() {
        // Create a simplified brain model using spheres and ellipsoids
        const brainGroup = new THREE.Group();
        brainGroup.userData.type = 'brainGroup';
        
        // Main brain geometry (ellipsoid)
        const brainGeometry = new THREE.SphereGeometry(5, 32, 16);
        brainGeometry.scale(1, 0.8, 1.2);
        
        const brainMaterial = new THREE.MeshPhongMaterial({
            color: 0xffc0cb,
            transparent: true,
            opacity: 0.8,
            shininess: 30
        });
        
        this.brainMesh = new THREE.Mesh(brainGeometry, brainMaterial);
        this.brainMesh.castShadow = true;
        this.brainMesh.receiveShadow = true;
        this.brainMesh.userData.type = 'brain';
        
        brainGroup.add(this.brainMesh);
        
        // Add brain hemispheres detail
        this.createBrainDetails(brainGroup);
        
        this.scene.add(brainGroup);
    }
    
    createBrainDetails(brainGroup) {
        // Left hemisphere
        const leftHemisphere = new THREE.SphereGeometry(4.8, 32, 16, 0, Math.PI);
        leftHemisphere.scale(1, 0.8, 1.2);
        const leftMaterial = new THREE.MeshPhongMaterial({
            color: 0xffb3ba,
            transparent: true,
            opacity: 0.9
        });
        const leftMesh = new THREE.Mesh(leftHemisphere, leftMaterial);
        leftMesh.position.x = -0.2;
        brainGroup.add(leftMesh);
        
        // Right hemisphere
        const rightHemisphere = new THREE.SphereGeometry(4.8, 32, 16, Math.PI, Math.PI);
        rightHemisphere.scale(1, 0.8, 1.2);
        const rightMaterial = new THREE.MeshPhongMaterial({
            color: 0xffdfba,
            transparent: true,
            opacity: 0.9
        });
        const rightMesh = new THREE.Mesh(rightHemisphere, rightMaterial);
        rightMesh.position.x = 0.2;
        brainGroup.add(rightMesh);
        
        // Brain stem
        const stemGeometry = new THREE.CylinderGeometry(0.8, 1.2, 3, 8);
        const stemMaterial = new THREE.MeshPhongMaterial({
            color: 0xffd1dc,
            transparent: true,
            opacity: 0.9
        });
        const stemMesh = new THREE.Mesh(stemGeometry, stemMaterial);
        stemMesh.position.set(0, -4, 0);
        brainGroup.add(stemMesh);
    }
    
    createTumorModel() {
        // Create tumor as an irregular sphere
        const tumorGeometry = new THREE.SphereGeometry(1.5, 16, 12);
        
        // Deform the geometry to make it irregular
        const vertices = tumorGeometry.attributes.position.array;
        for (let i = 0; i < vertices.length; i += 3) {
            const noise = (Math.random() - 0.5) * 0.3;
            vertices[i] += noise;
            vertices[i + 1] += noise;
            vertices[i + 2] += noise;
        }
        tumorGeometry.attributes.position.needsUpdate = true;
        tumorGeometry.computeVertexNormals();
        
        const tumorMaterial = new THREE.MeshPhongMaterial({
            color: 0xff4444,
            transparent: true,
            opacity: 0.9,
            shininess: 50,
            emissive: 0x220000
        });
        
        this.tumorMesh = new THREE.Mesh(tumorGeometry, tumorMaterial);
        this.tumorMesh.position.set(2, 1, -1);
        this.tumorMesh.castShadow = true;
        this.tumorMesh.userData.type = 'tumor';
        
        // Add pulsing animation to tumor
        this.tumorMesh.userData.originalScale = this.tumorMesh.scale.clone();
        
        const brainGroup = this.scene.children.find(child => child.userData.type === 'brainGroup');
        if (brainGroup) {
            brainGroup.add(this.tumorMesh);
        }
    }
    
    setupEventListeners() {
        // Window resize
        window.addEventListener('resize', () => this.onWindowResize());
        
        // Control buttons
        this.setupControlButtons();
    }
    
    setupControlButtons() {
        // View buttons
        const viewButtons = document.querySelectorAll('.view-btn');
        viewButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const view = e.target.dataset.view;
                this.setView(view);
                
                // Update active state
                viewButtons.forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
            });
        });
        
        // Tool buttons
        const toolButtons = document.querySelectorAll('[data-tool]');
        toolButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const tool = e.target.dataset.tool;
                this.setTool(tool);
                
                // Update active state
                toolButtons.forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
            });
        });
        
        // Opacity controls
        const brainOpacity = document.getElementById('brain-opacity');
        const tumorOpacity = document.getElementById('tumor-opacity');
        const rotationSpeed = document.getElementById('rotation-speed');
        
        if (brainOpacity) {
            brainOpacity.addEventListener('input', (e) => {
                this.setBrainOpacity(e.target.value / 100);
            });
        }
        
        if (tumorOpacity) {
            tumorOpacity.addEventListener('input', (e) => {
                this.setTumorOpacity(e.target.value / 100);
            });
        }
        
        if (rotationSpeed) {
            rotationSpeed.addEventListener('input', (e) => {
                this.rotationSpeed = e.target.value / 100;
            });
        }
    }
    
    setView(viewType) {
        if (!this.camera) return;
        
        const distance = 15;
        
        switch (viewType) {
            case 'front':
                this.camera.position.set(0, 0, distance);
                break;
            case 'side':
                this.camera.position.set(distance, 0, 0);
                break;
            case 'top':
                this.camera.position.set(0, distance, 0);
                break;
            case 'iso':
                this.camera.position.set(distance * 0.7, distance * 0.7, distance * 0.7);
                break;
        }
        
        this.camera.lookAt(0, 0, 0);
    }
    
    setTool(toolType) {
        this.selectedTool = toolType;
        this.segmentationMode = toolType;
        
        // Update cursor based on tool
        const canvas = this.renderer.domElement;
        switch (toolType) {
            case 'view':
                canvas.style.cursor = 'grab';
                break;
            case 'segment':
                canvas.style.cursor = 'crosshair';
                break;
            case 'measure':
                canvas.style.cursor = 'crosshair';
                break;
            case 'annotate':
                canvas.style.cursor = 'pointer';
                break;
        }
    }
    
    setBrainOpacity(opacity) {
        if (this.brainMesh) {
            this.brainMesh.material.opacity = opacity;
        }
    }
    
    setTumorOpacity(opacity) {
        if (this.tumorMesh) {
            this.tumorMesh.material.opacity = opacity;
        }
    }
    
    animate() {
        this.animationId = requestAnimationFrame(() => this.animate());
        
        // Update controls
        if (this.updateRotation) {
            this.updateRotation();
        }
        
        // Auto rotation
        if (this.autoRotate && this.rotationSpeed > 0) {
            const brainGroup = this.scene.children.find(child => child.userData.type === 'brainGroup');
            if (brainGroup) {
                brainGroup.rotation.y += this.rotationSpeed;
            }
        }
        
        // Animate tumor pulsing
        if (this.tumorMesh) {
            const time = Date.now() * 0.001;
            const scale = 1 + Math.sin(time * 2) * 0.1;
            this.tumorMesh.scale.setScalar(scale);
        }
        
        this.render();
    }
    
    render() {
        if (this.renderer && this.scene && this.camera) {
            this.renderer.render(this.scene, this.camera);
        }
    }
    
    onWindowResize() {
        if (!this.container || !this.camera || !this.renderer) return;
        
        const width = this.container.clientWidth;
        const height = this.container.clientHeight;
        
        this.camera.aspect = width / height;
        this.camera.updateProjectionMatrix();
        
        this.renderer.setSize(width, height);
    }
    
    // Analysis methods
    calculateTumorVolume() {
        if (!this.tumorMesh) return 0;
        
        // Simplified volume calculation
        const geometry = this.tumorMesh.geometry;
        const scale = this.tumorMesh.scale;
        const radius = 1.5; // Base radius
        const volume = (4/3) * Math.PI * Math.pow(radius, 3) * scale.x * scale.y * scale.z;
        
        return Math.round(volume * 100) / 100; // Round to 2 decimal places
    }
    
    calculateBrainVolume() {
        // Average human brain volume
        return 1400; // cm³
    }
    
    updateAnalysisResults() {
        const tumorVolume = this.calculateTumorVolume();
        const brainVolume = this.calculateBrainVolume();
        const tumorRatio = ((tumorVolume / brainVolume) * 100).toFixed(2);
        
        // Update UI elements
        const tumorVolumeEl = document.getElementById('tumor-volume');
        const brainVolumeEl = document.getElementById('brain-volume');
        const tumorRatioEl = document.getElementById('tumor-ratio');
        
        if (tumorVolumeEl) tumorVolumeEl.textContent = `${tumorVolume} cm³`;
        if (brainVolumeEl) brainVolumeEl.textContent = `${brainVolume} cm³`;
        if (tumorRatioEl) tumorRatioEl.textContent = `${tumorRatio}%`;
    }
    
    destroy() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }
        
        if (this.renderer) {
            this.renderer.dispose();
            if (this.container && this.renderer.domElement) {
                this.container.removeChild(this.renderer.domElement);
            }
        }
        
        // Clean up geometries and materials
        this.scene?.traverse((object) => {
            if (object.geometry) object.geometry.dispose();
            if (object.material) {
                if (Array.isArray(object.material)) {
                    object.material.forEach(material => material.dispose());
                } else {
                    object.material.dispose();
                }
            }
        });
    }
}

// Export for use in other modules
export default BrainViewer3D;

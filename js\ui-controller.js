// UI Controller for Enhanced User Experience

class UIController {
    constructor() {
        this.activeTooltips = new Set();
        this.notifications = [];
        this.shortcuts = new Map();
        this.theme = 'light';
        
        this.init();
    }
    
    init() {
        this.setupKeyboardShortcuts();
        this.setupTooltips();
        this.setupThemeToggle();
        this.setupAccessibility();
        this.setupAnimations();
        this.detectUserPreferences();
        
        console.log('UI Controller initialized');
    }
    
    setupKeyboardShortcuts() {
        // Define keyboard shortcuts
        this.shortcuts.set('ctrl+enter', () => this.runCurrentCode());
        this.shortcuts.set('ctrl+shift+c', () => this.clearOutput());
        this.shortcuts.set('ctrl+shift+r', () => this.resetView());
        this.shortcuts.set('f11', () => this.toggleFullscreen());
        this.shortcuts.set('ctrl+/', () => this.showShortcutsHelp());
        this.shortcuts.set('escape', () => this.closeModals());
        
        // Global keyboard event listener
        document.addEventListener('keydown', (e) => {
            const key = this.getKeyCombo(e);
            const action = this.shortcuts.get(key);
            
            if (action) {
                e.preventDefault();
                action();
            }
        });
    }
    
    getKeyCombo(event) {
        const parts = [];
        
        if (event.ctrlKey) parts.push('ctrl');
        if (event.shiftKey) parts.push('shift');
        if (event.altKey) parts.push('alt');
        if (event.metaKey) parts.push('meta');
        
        const key = event.key.toLowerCase();
        if (key !== 'control' && key !== 'shift' && key !== 'alt' && key !== 'meta') {
            parts.push(key);
        }
        
        return parts.join('+');
    }
    
    runCurrentCode() {
        // Run code in the currently active editor
        const activeSection = document.querySelector('.content-section.active');
        if (!activeSection) return;
        
        if (activeSection.id === 'matlab-basics-section') {
            const runBtn = document.getElementById('run-matlab-code');
            if (runBtn) runBtn.click();
        } else if (activeSection.id === 'exercises-section') {
            const runBtn = document.querySelector('.btn-run-exercise');
            if (runBtn) runBtn.click();
        }
    }
    
    clearOutput() {
        const activeSection = document.querySelector('.content-section.active');
        if (!activeSection) return;
        
        if (activeSection.id === 'matlab-basics-section') {
            const clearBtn = document.getElementById('clear-output');
            if (clearBtn) clearBtn.click();
        } else if (activeSection.id === 'exercises-section') {
            const clearBtn = document.querySelector('.btn-clear-output');
            if (clearBtn) clearBtn.click();
        }
    }
    
    resetView() {
        const resetBtn = document.getElementById('reset-view');
        if (resetBtn) resetBtn.click();
    }
    
    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen();
        } else {
            document.exitFullscreen();
        }
    }
    
    closeModals() {
        const modals = document.querySelectorAll('.modal.active');
        modals.forEach(modal => {
            modal.classList.remove('active');
        });
    }
    
    setupTooltips() {
        // Create tooltip element
        this.tooltip = document.createElement('div');
        this.tooltip.className = 'tooltip';
        this.tooltip.style.cssText = `
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 14px;
            pointer-events: none;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.2s;
            max-width: 200px;
            word-wrap: break-word;
        `;
        document.body.appendChild(this.tooltip);
        
        // Add tooltips to elements with data-tooltip attribute
        this.initializeTooltips();
        
        // Setup dynamic tooltip detection
        this.observeTooltipElements();
    }
    
    initializeTooltips() {
        const tooltipElements = document.querySelectorAll('[data-tooltip]');
        
        tooltipElements.forEach(element => {
            this.addTooltipListeners(element);
        });
    }
    
    addTooltipListeners(element) {
        element.addEventListener('mouseenter', (e) => {
            this.showTooltip(e.target, e.target.dataset.tooltip);
        });
        
        element.addEventListener('mouseleave', () => {
            this.hideTooltip();
        });
        
        element.addEventListener('mousemove', (e) => {
            this.updateTooltipPosition(e);
        });
    }
    
    showTooltip(element, text) {
        if (!text) return;
        
        this.tooltip.textContent = text;
        this.tooltip.style.opacity = '1';
        this.activeTooltips.add(element);
    }
    
    hideTooltip() {
        this.tooltip.style.opacity = '0';
        this.activeTooltips.clear();
    }
    
    updateTooltipPosition(event) {
        const x = event.clientX + 10;
        const y = event.clientY - 30;
        
        this.tooltip.style.left = `${x}px`;
        this.tooltip.style.top = `${y}px`;
    }
    
    observeTooltipElements() {
        // Use MutationObserver to detect new elements with tooltips
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        const tooltipElements = node.querySelectorAll('[data-tooltip]');
                        tooltipElements.forEach(element => {
                            this.addTooltipListeners(element);
                        });
                        
                        if (node.hasAttribute && node.hasAttribute('data-tooltip')) {
                            this.addTooltipListeners(node);
                        }
                    }
                });
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
    
    setupThemeToggle() {
        // Create theme toggle button
        const themeToggle = document.createElement('button');
        themeToggle.className = 'theme-toggle';
        themeToggle.innerHTML = '<i class="fas fa-moon"></i>';
        themeToggle.setAttribute('data-tooltip', 'Toggle dark mode');
        themeToggle.style.cssText = `
            position: fixed;
            top: 20px;
            right: 80px;
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 50%;
            background: var(--bg-primary);
            color: var(--text-primary);
            cursor: pointer;
            box-shadow: var(--shadow-md);
            z-index: 100;
            transition: all var(--transition-fast);
        `;
        
        themeToggle.addEventListener('click', () => {
            this.toggleTheme();
        });
        
        document.body.appendChild(themeToggle);
        this.themeToggle = themeToggle;
    }
    
    toggleTheme() {
        this.theme = this.theme === 'light' ? 'dark' : 'light';
        document.body.classList.toggle('dark-theme', this.theme === 'dark');
        
        // Update theme toggle icon
        const icon = this.themeToggle.querySelector('i');
        icon.className = this.theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        
        // Save preference
        localStorage.setItem('theme', this.theme);
        
        this.showNotification(`Switched to ${this.theme} theme`, 'info');
    }
    
    setupAccessibility() {
        // Add focus indicators
        this.addFocusIndicators();
        
        // Setup screen reader announcements
        this.setupScreenReaderAnnouncements();
        
        // Add skip links
        this.addSkipLinks();
        
        // Setup high contrast mode detection
        this.setupHighContrastMode();
    }
    
    addFocusIndicators() {
        const style = document.createElement('style');
        style.textContent = `
            .focus-visible {
                outline: 2px solid var(--primary-color);
                outline-offset: 2px;
            }
            
            button:focus-visible,
            input:focus-visible,
            textarea:focus-visible,
            select:focus-visible {
                outline: 2px solid var(--primary-color);
                outline-offset: 2px;
            }
        `;
        document.head.appendChild(style);
    }
    
    setupScreenReaderAnnouncements() {
        // Create live region for announcements
        this.liveRegion = document.createElement('div');
        this.liveRegion.setAttribute('aria-live', 'polite');
        this.liveRegion.setAttribute('aria-atomic', 'true');
        this.liveRegion.style.cssText = `
            position: absolute;
            left: -10000px;
            width: 1px;
            height: 1px;
            overflow: hidden;
        `;
        document.body.appendChild(this.liveRegion);
    }
    
    announceToScreenReader(message) {
        this.liveRegion.textContent = message;
        
        // Clear after announcement
        setTimeout(() => {
            this.liveRegion.textContent = '';
        }, 1000);
    }
    
    addSkipLinks() {
        const skipLinks = document.createElement('div');
        skipLinks.className = 'skip-links';
        skipLinks.innerHTML = `
            <a href="#main-content" class="skip-link">Skip to main content</a>
            <a href="#navigation" class="skip-link">Skip to navigation</a>
        `;
        skipLinks.style.cssText = `
            position: absolute;
            top: -40px;
            left: 6px;
            z-index: 1000;
        `;
        
        const skipLinkStyle = document.createElement('style');
        skipLinkStyle.textContent = `
            .skip-link {
                position: absolute;
                top: -40px;
                left: 6px;
                background: var(--primary-color);
                color: white;
                padding: 8px;
                text-decoration: none;
                border-radius: 4px;
                z-index: 1000;
                transition: top 0.3s;
            }
            
            .skip-link:focus {
                top: 6px;
            }
        `;
        document.head.appendChild(skipLinkStyle);
        document.body.insertBefore(skipLinks, document.body.firstChild);
    }
    
    setupHighContrastMode() {
        // Detect high contrast mode preference
        if (window.matchMedia('(prefers-contrast: high)').matches) {
            document.body.classList.add('high-contrast');
        }
        
        // Listen for changes
        window.matchMedia('(prefers-contrast: high)').addEventListener('change', (e) => {
            document.body.classList.toggle('high-contrast', e.matches);
        });
    }
    
    setupAnimations() {
        // Respect reduced motion preference
        if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
            document.body.classList.add('reduced-motion');
        }
        
        // Add smooth scrolling
        this.setupSmoothScrolling();
        
        // Add loading animations
        this.setupLoadingAnimations();
    }
    
    setupSmoothScrolling() {
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }
    
    setupLoadingAnimations() {
        // Add loading states to buttons
        this.observeLoadingButtons();
    }
    
    observeLoadingButtons() {
        document.addEventListener('click', (e) => {
            if (e.target.matches('button[data-loading]')) {
                this.showButtonLoading(e.target);
            }
        });
    }
    
    showButtonLoading(button) {
        const originalText = button.textContent;
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
        
        // Simulate loading (in real app, this would be tied to actual operations)
        setTimeout(() => {
            button.disabled = false;
            button.textContent = originalText;
        }, 2000);
    }
    
    detectUserPreferences() {
        // Load saved theme
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme) {
            this.theme = savedTheme;
            document.body.classList.toggle('dark-theme', this.theme === 'dark');
            
            if (this.themeToggle) {
                const icon = this.themeToggle.querySelector('i');
                icon.className = this.theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
            }
        } else {
            // Detect system preference
            if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
                this.toggleTheme();
            }
        }
        
        // Detect motion preference
        if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
            document.body.classList.add('reduced-motion');
        }
    }
    
    showNotification(message, type = 'info', duration = 3000) {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas ${this.getNotificationIcon(type)}"></i>
                <span>${message}</span>
            </div>
            <button class="notification-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        `;
        
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            min-width: 300px;
            max-width: 500px;
            padding: 16px;
            border-radius: 8px;
            box-shadow: var(--shadow-lg);
            z-index: 1000;
            animation: slideInRight 0.3s ease-out;
            margin-bottom: 10px;
        `;
        
        // Set colors based on type
        switch (type) {
            case 'success':
                notification.style.background = '#10b981';
                notification.style.color = 'white';
                break;
            case 'error':
                notification.style.background = '#ef4444';
                notification.style.color = 'white';
                break;
            case 'warning':
                notification.style.background = '#f59e0b';
                notification.style.color = 'white';
                break;
            default:
                notification.style.background = '#3b82f6';
                notification.style.color = 'white';
        }
        
        document.body.appendChild(notification);
        this.notifications.push(notification);
        
        // Auto remove
        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => {
                notification.remove();
                this.notifications = this.notifications.filter(n => n !== notification);
            }, 300);
        }, duration);
        
        // Announce to screen reader
        this.announceToScreenReader(message);
    }
    
    getNotificationIcon(type) {
        switch (type) {
            case 'success': return 'fa-check-circle';
            case 'error': return 'fa-exclamation-circle';
            case 'warning': return 'fa-exclamation-triangle';
            default: return 'fa-info-circle';
        }
    }
    
    showShortcutsHelp() {
        const shortcuts = [
            { key: 'Ctrl + Enter', description: 'Run current code' },
            { key: 'Ctrl + Shift + C', description: 'Clear output' },
            { key: 'Ctrl + Shift + R', description: 'Reset 3D view' },
            { key: 'F11', description: 'Toggle fullscreen' },
            { key: 'Ctrl + /', description: 'Show this help' },
            { key: 'Escape', description: 'Close modals' }
        ];
        
        const helpContent = `
            <h3>Keyboard Shortcuts</h3>
            <div class="shortcuts-list">
                ${shortcuts.map(shortcut => `
                    <div class="shortcut-item">
                        <kbd>${shortcut.key}</kbd>
                        <span>${shortcut.description}</span>
                    </div>
                `).join('')}
            </div>
        `;
        
        this.showModal('Keyboard Shortcuts', helpContent);
    }
    
    showModal(title, content) {
        const modal = document.getElementById('exercise-modal');
        if (!modal) return;
        
        const modalTitle = document.getElementById('modal-title');
        const modalBody = document.getElementById('modal-body');
        
        if (modalTitle) modalTitle.textContent = title;
        if (modalBody) modalBody.innerHTML = content;
        
        modal.classList.add('active');
        
        // Focus management
        const firstFocusable = modal.querySelector('button, input, textarea, select, a[href]');
        if (firstFocusable) firstFocusable.focus();
    }
    
    // Performance monitoring
    monitorPerformance() {
        if ('performance' in window) {
            const observer = new PerformanceObserver((list) => {
                list.getEntries().forEach((entry) => {
                    if (entry.entryType === 'measure') {
                        console.log(`Performance: ${entry.name} took ${entry.duration}ms`);
                    }
                });
            });
            
            observer.observe({ entryTypes: ['measure'] });
        }
    }
    
    // Error handling
    setupErrorHandling() {
        window.addEventListener('error', (e) => {
            console.error('Global error:', e.error);
            this.showNotification('An unexpected error occurred', 'error');
        });
        
        window.addEventListener('unhandledrejection', (e) => {
            console.error('Unhandled promise rejection:', e.reason);
            this.showNotification('An unexpected error occurred', 'error');
        });
    }
}

// Make class available globally
window.UIController = UIController;

// Initialize UI Controller when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    if (!window.uiController) {
        window.uiController = new UIController();
    }
});


import React from 'react';

interface SectionCardProps {
  title: string;
  children: React.ReactNode;
  icon?: React.ReactNode;
}

const SectionCard: React.FC<SectionCardProps> = ({ title, children, icon }) => {
  return (
    <div className="bg-white dark:bg-slate-800 rounded-xl shadow-lg p-6 mb-8 transition-shadow duration-300 hover:shadow-xl">
      <div className="flex items-center mb-4">
        {icon && <div className="me-3 text-sky-500 dark:text-sky-400">{icon}</div>}
        <h3 className="text-xl font-bold text-slate-800 dark:text-slate-100">{title}</h3>
      </div>
      <div className="prose prose-slate dark:prose-invert max-w-none prose-p:text-slate-600 dark:prose-p:text-slate-300 prose-ul:list-disc prose-ul:ms-6">
        {children}
      </div>
    </div>
  );
};

export default SectionCard;

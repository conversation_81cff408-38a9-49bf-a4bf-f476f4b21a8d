/* Components CSS for Interactive Elements */

/* Split Layout for MATLAB Section */
.split-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
    height: calc(100vh - 200px);
}

.tutorial-panel {
    background: var(--bg-primary);
    border: 1px solid var(--bg-tertiary);
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.tutorial-content {
    padding: var(--spacing-xl);
    height: 100%;
    overflow-y: auto;
}

.code-panel {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

/* Code Editor */
.code-editor-container {
    flex: 1;
    background: var(--bg-primary);
    border: 1px solid var(--bg-tertiary);
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--bg-tertiary);
}

.editor-title {
    font-weight: 600;
    color: var(--text-primary);
}

.editor-controls {
    display: flex;
    gap: var(--spacing-sm);
}

.btn-run, .btn-clear {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border: none;
    border-radius: var(--radius-sm);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.btn-run {
    background: var(--secondary-color);
    color: white;
}

.btn-run:hover {
    background: #059669;
}

.btn-clear {
    background: var(--danger-color);
    color: white;
}

.btn-clear:hover {
    background: #dc2626;
}

.code-editor {
    width: 100%;
    height: 300px;
    border: none;
    outline: none;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
    line-height: 1.5;
    padding: var(--spacing-md);
    background: #2d3748;
    color: #e2e8f0;
    resize: none;
}

/* Output Container */
.output-container {
    height: 200px;
    background: var(--bg-primary);
    border: 1px solid var(--bg-tertiary);
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.output-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--bg-tertiary);
}

.output-title {
    font-weight: 600;
    color: var(--text-primary);
}

.btn-clear-output {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
}

.btn-clear-output:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.code-output {
    padding: var(--spacing-md);
    height: calc(100% - 60px);
    overflow-y: auto;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
    line-height: 1.5;
    background: #1a202c;
    color: #e2e8f0;
}

/* DICOM Layout */
.dicom-layout {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: var(--spacing-xl);
    height: calc(100vh - 200px);
}

.dicom-controls {
    background: var(--bg-primary);
    border: 1px solid var(--bg-tertiary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    overflow-y: auto;
}

.file-upload-area {
    margin-bottom: var(--spacing-xl);
}

.upload-label {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-xl);
    border: 2px dashed var(--bg-tertiary);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-fast);
    text-align: center;
}

.upload-label:hover {
    border-color: var(--primary-color);
    background: var(--bg-secondary);
}

.upload-label i {
    font-size: 2rem;
    color: var(--primary-color);
}

#dicom-file-input {
    display: none;
}

.processing-controls h3 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.control-group {
    margin-bottom: var(--spacing-md);
}

.control-group label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
}

.control-group input[type="range"] {
    width: 100%;
    height: 6px;
    border-radius: var(--radius-sm);
    background: var(--bg-tertiary);
    outline: none;
    cursor: pointer;
}

.control-group input[type="range"]::-webkit-slider-thumb {
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
}

.control-group input[type="range"]::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
    border: none;
}

/* DICOM Viewer */
.dicom-viewer {
    background: var(--bg-primary);
    border: 1px solid var(--bg-tertiary);
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.viewer-container {
    position: relative;
    height: calc(100% - 60px);
}

.dicom-canvas {
    width: 100%;
    height: 100%;
    object-fit: contain;
    background: #000;
}

.viewer-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: var(--spacing-md);
}

.slice-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

#slice-slider {
    flex: 1;
}

/* Segmentation Layout */
.segmentation-layout {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: var(--spacing-xl);
    height: calc(100vh - 200px);
}

.segmentation-controls {
    background: var(--bg-primary);
    border: 1px solid var(--bg-tertiary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.model-controls h3,
.segmentation-tools h3,
.analysis-results h3 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.segmentation-tools {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.analysis-results {
    background: var(--bg-secondary);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
}

.result-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--bg-tertiary);
}

.result-item:last-child {
    border-bottom: none;
}

.result-item .label {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.result-item .value {
    font-weight: 600;
    color: var(--text-primary);
}

/* 3D Viewer */
.viewer-3d {
    background: var(--bg-primary);
    border: 1px solid var(--bg-tertiary);
    border-radius: var(--radius-lg);
    overflow: hidden;
    position: relative;
}

.three-js-container {
    width: 100%;
    height: calc(100% - 60px);
    background: linear-gradient(135deg, #1e293b, #334155);
}

.viewer-controls {
    display: flex;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-top: 1px solid var(--bg-tertiary);
}

.view-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--bg-tertiary);
    background: var(--bg-primary);
    color: var(--text-secondary);
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
    font-size: 0.875rem;
}

.view-btn:hover,
.view-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Exercises Layout */
.exercises-layout {
    display: grid;
    grid-template-columns: 400px 1fr;
    gap: var(--spacing-xl);
    height: calc(100vh - 200px);
}

.exercise-list {
    background: var(--bg-primary);
    border: 1px solid var(--bg-tertiary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    overflow-y: auto;
}

.exercise-category {
    margin-bottom: var(--spacing-xl);
}

.exercise-category h3 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--primary-color);
}

.exercise-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    border: 1px solid var(--bg-tertiary);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.exercise-item:hover {
    background: var(--bg-secondary);
    border-color: var(--primary-color);
}

.exercise-info h4 {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.exercise-info p {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
}

.difficulty {
    font-size: 0.75rem;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-weight: 500;
}

.difficulty.easy {
    background: #dcfce7;
    color: #166534;
}

.difficulty.medium {
    background: #fef3c7;
    color: #92400e;
}

.difficulty.hard {
    background: #fee2e2;
    color: #991b1b;
}

.exercise-status i {
    color: var(--text-muted);
    font-size: 1.25rem;
}

.exercise-status i.completed {
    color: var(--secondary-color);
}

.exercise-workspace {
    background: var(--bg-primary);
    border: 1px solid var(--bg-tertiary);
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.exercise-content {
    padding: var(--spacing-xl);
    height: 100%;
    overflow-y: auto;
}

.welcome-message {
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--text-secondary);
}

.welcome-message i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: var(--spacing-lg);
}

.welcome-message h3 {
    font-size: 1.5rem;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    max-width: 800px;
    width: 90%;
    max-height: 80%;
    overflow: hidden;
    box-shadow: var(--shadow-xl);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--bg-tertiary);
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
}

.modal-close:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.modal-body {
    padding: var(--spacing-lg);
    overflow-y: auto;
    max-height: 60vh;
}

/* Exercise Interface Styles */
.exercise-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 2px solid var(--bg-tertiary);
}

.exercise-meta {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
}

.exercise-instructions {
    background: var(--bg-secondary);
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    margin-bottom: var(--spacing-xl);
}

.exercise-workspace {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr auto;
    gap: var(--spacing-lg);
    height: 500px;
}

.code-section {
    grid-column: 1;
    grid-row: 1;
}

.output-section {
    grid-column: 2;
    grid-row: 1;
}

.tests-section {
    grid-column: 1 / -1;
    grid-row: 2;
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md);
}

.exercise-code-editor {
    width: 100%;
    height: 300px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
    border: 1px solid var(--bg-tertiary);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    background: #2d3748;
    color: #e2e8f0;
    resize: vertical;
}

.exercise-output {
    height: 300px;
    overflow-y: auto;
    background: #1a202c;
    color: #e2e8f0;
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
}

.test-results {
    margin-top: var(--spacing-md);
}

.test-result {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
    margin-bottom: var(--spacing-xs);
    border-radius: var(--radius-sm);
}

.test-result.passed {
    background: #dcfce7;
    color: #166534;
}

.test-result.failed {
    background: #fee2e2;
    color: #991b1b;
}

.test-status {
    font-weight: 600;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
}

.test-status.all-passed {
    background: #dcfce7;
    color: #166534;
}

.test-status.some-failed {
    background: #fee2e2;
    color: #991b1b;
}

.exercise-actions {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    margin-top: var(--spacing-xl);
}

.btn-hint,
.btn-solution,
.btn-submit {
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-md);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.btn-hint {
    background: var(--accent-color);
    color: white;
}

.btn-solution {
    background: var(--text-secondary);
    color: white;
}

.btn-submit {
    background: var(--secondary-color);
    color: white;
}

.btn-submit:disabled {
    background: var(--text-muted);
    cursor: not-allowed;
}

/* Responsive Design for Components */
@media (max-width: 1024px) {
    .split-layout,
    .dicom-layout,
    .segmentation-layout,
    .exercises-layout {
        grid-template-columns: 1fr;
        height: auto;
    }

    .exercise-workspace {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto auto;
        height: auto;
    }

    .code-section,
    .output-section,
    .tests-section {
        grid-column: 1;
    }

    .sidebar {
        order: 2;
    }

    .content-panels {
        order: 1;
    }
}

@media (max-width: 768px) {
    .code-editor,
    .exercise-code-editor {
        height: 200px;
    }

    .output-container,
    .exercise-output {
        height: 150px;
    }

    .modal-content {
        width: 95%;
        margin: var(--spacing-md);
    }

    .exercise-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-md);
    }

    .exercise-actions {
        flex-direction: column;
        align-items: stretch;
    }
}

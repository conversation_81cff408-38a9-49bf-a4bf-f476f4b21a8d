<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Brain Tumor Segmentation - Interactive Learning Platform</title>
    <meta name="description" content="Interactive platform for learning MATLAB programming and medical image processing with 3D brain tumor segmentation">

    <!-- Fonts and Icons -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- External Libraries -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js" defer></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.js" defer></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/octave/octave.min.js" defer></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/plotly.js/2.18.0/plotly.min.js" defer></script>

    <!-- CodeMirror CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/theme/monokai.min.css">

    <!-- Inline Critical CSS -->
    <style>
        /* Critical CSS for immediate display */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #2563eb;
            --secondary-color: #10b981;
            --accent-color: #f59e0b;
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-tertiary: #e2e8f0;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --text-muted: #94a3b8;
            --spacing-sm: 0.5rem;
            --spacing-md: 1rem;
            --spacing-lg: 1.5rem;
            --spacing-xl: 2rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            --transition-fast: 150ms ease-in-out;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background-color: var(--bg-primary);
            overflow-x: hidden;
        }

        /* Loading Screen */
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            color: white;
        }

        .loading-content {
            text-align: center;
            max-width: 400px;
            padding: var(--spacing-xl);
        }

        .brain-loader {
            position: relative;
            width: 80px;
            height: 60px;
            margin: 0 auto var(--spacing-lg);
        }

        .brain-hemisphere {
            position: absolute;
            width: 35px;
            height: 50px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
            animation: pulse 2s ease-in-out infinite;
        }

        .brain-hemisphere.left {
            left: 0;
            animation-delay: 0s;
        }

        .brain-hemisphere.right {
            right: 0;
            animation-delay: 0.5s;
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.7; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.1); }
        }

        .loading-bar {
            width: 100%;
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: var(--radius-sm);
            overflow: hidden;
            margin-top: var(--spacing-lg);
        }

        .loading-progress {
            height: 100%;
            background: white;
            border-radius: var(--radius-sm);
            animation: loading 3s ease-in-out infinite;
        }

        @keyframes loading {
            0% { width: 0%; }
            50% { width: 70%; }
            100% { width: 100%; }
        }

        /* App Container */
        .app-container {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* Header */
        .app-header {
            background: var(--bg-primary);
            border-bottom: 1px solid var(--bg-tertiary);
            box-shadow: var(--shadow-sm);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: var(--spacing-md) var(--spacing-xl);
            max-width: 1400px;
            margin: 0 auto;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }

        .logo-section i {
            font-size: 2rem;
            color: var(--primary-color);
        }

        .logo-section h1 {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
        }

        .subtitle {
            font-size: 0.875rem;
            color: var(--text-secondary);
            font-weight: 500;
        }

        /* Navigation */
        .main-nav {
            display: flex;
            gap: var(--spacing-sm);
        }

        .nav-btn {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            padding: var(--spacing-sm) var(--spacing-md);
            border: none;
            background: transparent;
            color: var(--text-secondary);
            font-weight: 500;
            border-radius: var(--radius-md);
            cursor: pointer;
            transition: all var(--transition-fast);
            white-space: nowrap;
        }

        .nav-btn:hover {
            background: var(--bg-secondary);
            color: var(--text-primary);
        }

        .nav-btn.active {
            background: var(--primary-color);
            color: white;
        }

        /* Main Content */
        .main-content {
            flex: 1;
            display: flex;
            max-width: 1400px;
            margin: 0 auto;
            width: 100%;
        }

        /* Sidebar */
        .sidebar {
            width: 280px;
            background: var(--bg-secondary);
            border-right: 1px solid var(--bg-tertiary);
            padding: var(--spacing-lg);
            overflow-y: auto;
            height: calc(100vh - 80px);
        }

        /* Content Panels */
        .content-panels {
            flex: 1;
            padding: var(--spacing-xl);
            overflow-y: auto;
            height: calc(100vh - 80px);
        }

        .content-section {
            display: none;
        }

        .content-section.active {
            display: block;
        }

        .section-header h2 {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: var(--spacing-sm);
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }

        .section-header p {
            font-size: 1.125rem;
            color: var(--text-secondary);
            margin-bottom: var(--spacing-xl);
        }

        /* Overview Grid */
        .overview-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--spacing-xl);
        }

        .feature-card {
            background: var(--bg-primary);
            border: 1px solid var(--bg-tertiary);
            border-radius: var(--radius-lg);
            padding: var(--spacing-xl);
            text-align: center;
            transition: all var(--transition-fast);
            box-shadow: var(--shadow-sm);
        }

        .feature-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-md);
            border-color: var(--primary-color);
        }

        .card-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto var(--spacing-lg);
        }

        .card-icon i {
            font-size: 1.5rem;
            color: white;
        }

        .feature-card h3 {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-sm);
        }

        .feature-card p {
            color: var(--text-secondary);
            margin-bottom: var(--spacing-lg);
            line-height: 1.6;
        }

        .card-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: var(--spacing-sm) var(--spacing-lg);
            border-radius: var(--radius-md);
            font-weight: 500;
            cursor: pointer;
            transition: all var(--transition-fast);
        }

        .card-btn:hover {
            background: #1d4ed8;
            transform: translateY(-1px);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: var(--spacing-md);
                padding: var(--spacing-md);
            }

            .main-nav {
                flex-wrap: wrap;
                justify-content: center;
            }

            .main-content {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                height: auto;
                border-right: none;
                border-bottom: 1px solid var(--bg-tertiary);
            }

            .overview-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Utility Classes */
        .hidden { display: none !important; }
        .visible { display: block !important; }
    </style>
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-content">
            <div class="brain-loader">
                <div class="brain-hemisphere left"></div>
                <div class="brain-hemisphere right"></div>
            </div>
            <h2>Loading 3D Brain Tumor Segmentation Platform</h2>
            <div class="loading-bar">
                <div class="loading-progress"></div>
            </div>
        </div>
    </div>

    <!-- Main Application -->
    <div id="app" class="app-container" style="display: none;">
        <!-- Header -->
        <header class="app-header">
            <div class="header-content">
                <div class="logo-section">
                    <i class="fas fa-brain"></i>
                    <h1>3D Brain Tumor Segmentation</h1>
                    <span class="subtitle">Interactive Learning Platform</span>
                </div>
                <nav class="main-nav">
                    <button class="nav-btn active" data-section="overview">
                        <i class="fas fa-home"></i> Overview
                    </button>
                    <button class="nav-btn" data-section="matlab-basics">
                        <i class="fas fa-code"></i> MATLAB Basics
                    </button>
                    <button class="nav-btn" data-section="dicom-processing">
                        <i class="fas fa-file-medical"></i> DICOM Processing
                    </button>
                    <button class="nav-btn" data-section="3d-segmentation">
                        <i class="fas fa-cube"></i> 3D Segmentation
                    </button>
                    <button class="nav-btn" data-section="exercises">
                        <i class="fas fa-tasks"></i> Exercises
                    </button>
                </nav>
                <div class="user-progress">
                    <div class="progress-circle">
                        <span class="progress-text">0%</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content Area -->
        <main class="main-content">
            <!-- Sidebar -->
            <aside class="sidebar">
                <div class="sidebar-content">
                    <div class="section-menu" id="section-menu">
                        <!-- Dynamic content based on active section -->
                    </div>
                    
                    <div class="tools-panel">
                        <h3><i class="fas fa-tools"></i> Tools</h3>
                        <button class="tool-btn" id="reset-view">
                            <i class="fas fa-undo"></i> Reset View
                        </button>
                        <button class="tool-btn" id="toggle-wireframe">
                            <i class="fas fa-project-diagram"></i> Wireframe
                        </button>
                        <button class="tool-btn" id="export-model">
                            <i class="fas fa-download"></i> Export
                        </button>
                    </div>
                </div>
            </aside>

            <!-- Content Panels -->
            <div class="content-panels">
                <!-- Overview Section -->
                <section id="overview-section" class="content-section active">
                    <div class="section-header">
                        <h2><i class="fas fa-brain"></i> 3D Brain Tumor Segmentation Platform</h2>
                        <p>Learn MATLAB programming and medical image processing through interactive 3D visualization</p>
                    </div>
                    
                    <div class="overview-grid">
                        <div class="feature-card">
                            <div class="card-icon">
                                <i class="fas fa-code"></i>
                            </div>
                            <h3>MATLAB Programming</h3>
                            <p>Master MATLAB fundamentals with interactive coding exercises and real-time feedback</p>
                            <button class="card-btn" data-navigate="matlab-basics">Start Learning</button>
                        </div>
                        
                        <div class="feature-card">
                            <div class="card-icon">
                                <i class="fas fa-file-medical"></i>
                            </div>
                            <h3>DICOM Processing</h3>
                            <p>Learn medical image processing techniques for DICOM files and visualization</p>
                            <button class="card-btn" data-navigate="dicom-processing">Explore DICOM</button>
                        </div>
                        
                        <div class="feature-card">
                            <div class="card-icon">
                                <i class="fas fa-cube"></i>
                            </div>
                            <h3>3D Segmentation</h3>
                            <p>Interactive 3D brain tumor segmentation with advanced visualization tools</p>
                            <button class="card-btn" data-navigate="3d-segmentation">View 3D Model</button>
                        </div>
                        
                        <div class="feature-card">
                            <div class="card-icon">
                                <i class="fas fa-tasks"></i>
                            </div>
                            <h3>Practice Exercises</h3>
                            <p>Hands-on coding challenges and assessments to test your knowledge</p>
                            <button class="card-btn" data-navigate="exercises">Start Practicing</button>
                        </div>
                    </div>
                </section>

                <!-- MATLAB Basics Section -->
                <section id="matlab-basics-section" class="content-section">
                    <div class="section-header">
                        <h2><i class="fas fa-code"></i> MATLAB Programming Fundamentals</h2>
                        <p>Interactive tutorials and coding exercises for MATLAB basics</p>
                    </div>
                    
                    <div class="split-layout">
                        <div class="tutorial-panel">
                            <div class="tutorial-content" id="matlab-tutorial-content">
                                <h3>MATLAB Programming Tutorial</h3>
                                <div style="background: #f8fafc; padding: 1rem; border-radius: 0.5rem; margin: 1rem 0;">
                                    <h4>Getting Started with MATLAB</h4>
                                    <p>MATLAB is a powerful programming language for technical computing. Let's start with basic operations:</p>

                                    <h5 style="margin-top: 1rem;">1. Variables and Basic Operations</h5>
                                    <pre style="background: #1a202c; color: #e2e8f0; padding: 0.5rem; border-radius: 0.25rem; font-size: 0.875rem;">
% Create variables
x = 10;
y = 5;
result = x + y;
disp(result);</pre>

                                    <h5 style="margin-top: 1rem;">2. Arrays and Matrices</h5>
                                    <pre style="background: #1a202c; color: #e2e8f0; padding: 0.5rem; border-radius: 0.25rem; font-size: 0.875rem;">
% Create arrays
arr = [1, 2, 3, 4, 5];
matrix = [1, 2; 3, 4];
disp(matrix);</pre>

                                    <h5 style="margin-top: 1rem;">3. Plotting</h5>
                                    <pre style="background: #1a202c; color: #e2e8f0; padding: 0.5rem; border-radius: 0.25rem; font-size: 0.875rem;">
% Create a simple plot
t = 0:0.1:2*pi;
y = sin(t);
plot(t, y);
title('Sine Wave');</pre>

                                    <p style="margin-top: 1rem; color: #10b981; font-weight: 500;">
                                        💡 Try copying these examples to the code editor and click "Run" to see the results!
                                    </p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="code-panel">
                            <div class="code-editor-container">
                                <div class="editor-header">
                                    <span class="editor-title">MATLAB Code Editor</span>
                                    <div class="editor-controls">
                                        <button class="btn-run" id="run-matlab-code">
                                            <i class="fas fa-play"></i> Run
                                        </button>
                                        <button class="btn-clear" id="clear-matlab-code">
                                            <i class="fas fa-trash"></i> Clear
                                        </button>
                                    </div>
                                </div>
                                <textarea id="matlab-editor" class="code-editor" placeholder="% Enter your MATLAB code here
% Example:
x = 10;
y = 5;
result = x + y;
disp(['Result: ', num2str(result)]);"></textarea>
                            </div>
                            
                            <div class="output-container">
                                <div class="output-header">
                                    <span class="output-title">Output</span>
                                    <button class="btn-clear-output" id="clear-output">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                                <div id="matlab-output" class="code-output"></div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- DICOM Processing Section -->
                <section id="dicom-processing-section" class="content-section">
                    <div class="section-header">
                        <h2><i class="fas fa-file-medical"></i> DICOM Image Processing</h2>
                        <p>Learn medical image processing with DICOM format</p>
                    </div>
                    
                    <div class="dicom-layout">
                        <div class="dicom-controls">
                            <div class="file-upload-area">
                                <input type="file" id="dicom-file-input" accept=".dcm,.dicom" multiple>
                                <label for="dicom-file-input" class="upload-label">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                    <span>Upload DICOM Files</span>
                                </label>
                            </div>
                            
                            <div class="processing-controls">
                                <h3>Image Processing</h3>
                                <div class="control-group">
                                    <label>Brightness</label>
                                    <input type="range" id="brightness-slider" min="-100" max="100" value="0">
                                </div>
                                <div class="control-group">
                                    <label>Contrast</label>
                                    <input type="range" id="contrast-slider" min="0" max="200" value="100">
                                </div>
                                <div class="control-group">
                                    <label>Window Level</label>
                                    <input type="range" id="window-level-slider" min="0" max="4096" value="2048">
                                </div>
                                <div class="control-group">
                                    <label>Window Width</label>
                                    <input type="range" id="window-width-slider" min="1" max="4096" value="1024">
                                </div>
                            </div>
                        </div>
                        
                        <div class="dicom-viewer">
                            <div class="viewer-container">
                                <canvas id="dicom-canvas" class="dicom-canvas"></canvas>
                                <div class="viewer-info">
                                    <div class="slice-info">
                                        <span>Slice: <span id="current-slice">1</span> / <span id="total-slices">1</span></span>
                                        <input type="range" id="slice-slider" min="1" max="1" value="1">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 3D Segmentation Section -->
                <section id="3d-segmentation-section" class="content-section">
                    <div class="section-header">
                        <h2><i class="fas fa-cube"></i> 3D Brain Tumor Segmentation</h2>
                        <p>Interactive 3D visualization and tumor segmentation</p>
                    </div>

                    <div class="segmentation-layout">
                        <div class="segmentation-controls">
                            <div class="model-controls">
                                <h3>3D Model Controls</h3>
                                <div class="control-group">
                                    <label>Opacity</label>
                                    <input type="range" id="brain-opacity" min="0" max="100" value="80">
                                </div>
                                <div class="control-group">
                                    <label>Tumor Opacity</label>
                                    <input type="range" id="tumor-opacity" min="0" max="100" value="90">
                                </div>
                                <div class="control-group">
                                    <label>Rotation Speed</label>
                                    <input type="range" id="rotation-speed" min="0" max="10" value="1">
                                </div>
                            </div>

                            <div class="segmentation-tools">
                                <h3>Segmentation Tools</h3>
                                <button class="tool-btn active" data-tool="view">
                                    <i class="fas fa-eye"></i> View
                                </button>
                                <button class="tool-btn" data-tool="segment">
                                    <i class="fas fa-cut"></i> Segment
                                </button>
                                <button class="tool-btn" data-tool="measure">
                                    <i class="fas fa-ruler"></i> Measure
                                </button>
                                <button class="tool-btn" data-tool="annotate">
                                    <i class="fas fa-comment"></i> Annotate
                                </button>
                            </div>

                            <div class="analysis-results">
                                <h3>Analysis Results</h3>
                                <div class="result-item">
                                    <span class="label">Tumor Volume:</span>
                                    <span class="value" id="tumor-volume">4.2 cm³</span>
                                </div>
                                <div class="result-item">
                                    <span class="label">Brain Volume:</span>
                                    <span class="value" id="brain-volume">1400 cm³</span>
                                </div>
                                <div class="result-item">
                                    <span class="label">Tumor Ratio:</span>
                                    <span class="value" id="tumor-ratio">0.3%</span>
                                </div>
                                <div style="margin-top: 1rem; padding: 0.75rem; background: #f0f9ff; border-radius: 0.375rem; border: 1px solid #0ea5e9;">
                                    <p style="font-size: 0.875rem; color: #0369a1; margin: 0;">
                                        <i class="fas fa-info-circle"></i> Sample analysis data shown. Initialize 3D viewer for real-time calculations.
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div class="viewer-3d">
                            <div id="three-js-container" class="three-js-container">
                                <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: white; text-align: center;">
                                    <div>
                                        <i class="fas fa-cube" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.7;"></i>
                                        <p>3D Brain Viewer</p>
                                        <p style="font-size: 0.875rem; opacity: 0.7;">Click a navigation button to initialize the 3D view</p>
                                    </div>
                                </div>
                            </div>

                            <div class="viewer-controls">
                                <button class="view-btn" onclick="window.app.init3DViewer()">Initialize 3D</button>
                                <button class="view-btn" data-view="front">Front</button>
                                <button class="view-btn" data-view="side">Side</button>
                                <button class="view-btn" data-view="top">Top</button>
                                <button class="view-btn" data-view="iso">Isometric</button>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Exercises Section -->
                <section id="exercises-section" class="content-section">
                    <div class="section-header">
                        <h2><i class="fas fa-tasks"></i> Interactive Exercises</h2>
                        <p>Practice your skills with hands-on coding challenges</p>
                    </div>

                    <div class="exercises-layout">
                        <div class="exercise-list">
                            <div class="exercise-category">
                                <h3>MATLAB Fundamentals</h3>
                                <div class="exercise-item" data-exercise="matlab-1">
                                    <div class="exercise-info">
                                        <h4>Variables and Arrays</h4>
                                        <p>Learn basic variable operations and array manipulation</p>
                                        <span class="difficulty easy">Easy</span>
                                    </div>
                                    <div class="exercise-status">
                                        <i class="fas fa-circle"></i>
                                    </div>
                                </div>

                                <div class="exercise-item" data-exercise="matlab-2">
                                    <div class="exercise-info">
                                        <h4>Matrix Operations</h4>
                                        <p>Practice matrix multiplication and transformations</p>
                                        <span class="difficulty medium">Medium</span>
                                    </div>
                                    <div class="exercise-status">
                                        <i class="fas fa-circle"></i>
                                    </div>
                                </div>
                            </div>

                            <div class="exercise-category">
                                <h3>Image Processing</h3>
                                <div class="exercise-item" data-exercise="image-1">
                                    <div class="exercise-info">
                                        <h4>Image Filtering</h4>
                                        <p>Apply filters to medical images</p>
                                        <span class="difficulty medium">Medium</span>
                                    </div>
                                    <div class="exercise-status">
                                        <i class="fas fa-circle"></i>
                                    </div>
                                </div>

                                <div class="exercise-item" data-exercise="image-2">
                                    <div class="exercise-info">
                                        <h4>Tumor Detection</h4>
                                        <p>Implement basic tumor detection algorithms</p>
                                        <span class="difficulty hard">Hard</span>
                                    </div>
                                    <div class="exercise-status">
                                        <i class="fas fa-circle"></i>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="exercise-workspace">
                            <div id="exercise-content" class="exercise-content">
                                <div class="welcome-message">
                                    <i class="fas fa-rocket"></i>
                                    <h3>Ready to Practice?</h3>
                                    <p>Select an exercise from the left panel to get started!</p>

                                    <div style="margin-top: 2rem; padding: 1.5rem; background: #f8fafc; border-radius: 0.75rem; text-align: left;">
                                        <h4 style="color: #1e293b; margin-bottom: 1rem;">Quick Start Guide:</h4>
                                        <ol style="color: #64748b; line-height: 1.6;">
                                            <li>Choose an exercise difficulty level (Easy, Medium, Hard)</li>
                                            <li>Read the exercise instructions carefully</li>
                                            <li>Write your MATLAB code in the editor</li>
                                            <li>Click "Run" to test your code</li>
                                            <li>Click "Check" to validate your solution</li>
                                            <li>Submit when all tests pass!</li>
                                        </ol>

                                        <div style="margin-top: 1rem; padding: 1rem; background: #dcfce7; border-radius: 0.5rem;">
                                            <p style="color: #166534; font-size: 0.875rem; margin: 0;">
                                                <i class="fas fa-lightbulb"></i> <strong>Tip:</strong> Start with the "Variables and Arrays" exercise to get familiar with the platform!
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </main>

        <!-- Footer -->
        <footer class="app-footer">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>Learning Platform</h4>
                    <p>Interactive 3D Brain Tumor Segmentation</p>
                </div>
                <div class="footer-section">
                    <h4>Technologies</h4>
                    <p>Three.js • MATLAB • DICOM • WebGL</p>
                </div>
                <div class="footer-section">
                    <h4>Progress</h4>
                    <div class="progress-stats">
                        <span>Exercises Completed: <strong id="completed-exercises">0</strong></span>
                        <span>Total Score: <strong id="total-score">0</strong></span>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <!-- Modal for Exercise Details -->
    <div id="exercise-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">Exercise Title</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body" id="modal-body">
                <!-- Exercise content will be loaded here -->
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // Simple Application Controller
        class SimpleBrainApp {
            constructor() {
                this.currentSection = 'overview';
                this.init();
            }

            init() {
                console.log('Initializing Brain Tumor Segmentation Platform...');
                this.hideLoadingScreen();
                this.setupNavigation();
                this.setupInteractions();
                console.log('Platform ready!');
            }

            hideLoadingScreen() {
                setTimeout(() => {
                    const loadingScreen = document.getElementById('loading-screen');
                    const app = document.getElementById('app');

                    if (loadingScreen) {
                        loadingScreen.style.opacity = '0';
                        setTimeout(() => {
                            loadingScreen.style.display = 'none';
                            if (app) app.style.display = 'block';
                        }, 500);
                    }
                }, 2000);
            }

            setupNavigation() {
                const navButtons = document.querySelectorAll('.nav-btn');
                navButtons.forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        const section = e.target.dataset.section || e.target.closest('.nav-btn').dataset.section;
                        this.navigateToSection(section);
                    });
                });

                const cardButtons = document.querySelectorAll('.card-btn');
                cardButtons.forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        const section = e.target.dataset.navigate;
                        if (section) {
                            this.navigateToSection(section);
                        }
                    });
                });
            }

            navigateToSection(sectionId) {
                console.log(`Navigating to: ${sectionId}`);

                // Update navigation buttons
                const navButtons = document.querySelectorAll('.nav-btn');
                navButtons.forEach(btn => {
                    btn.classList.remove('active');
                    if (btn.dataset.section === sectionId) {
                        btn.classList.add('active');
                    }
                });

                // Update content sections
                const sections = document.querySelectorAll('.content-section');
                sections.forEach(section => {
                    section.classList.remove('active');
                });

                const targetSection = document.getElementById(`${sectionId}-section`);
                if (targetSection) {
                    targetSection.classList.add('active');
                }

                this.currentSection = sectionId;
                this.updateSectionMenu(sectionId);
            }

            updateSectionMenu(sectionId) {
                const sectionMenu = document.getElementById('section-menu');
                if (!sectionMenu) return;

                let menuContent = '';

                switch (sectionId) {
                    case 'overview':
                        menuContent = `
                            <h3><i class="fas fa-home"></i> Overview</h3>
                            <div style="padding: 1rem; background: #f8fafc; border-radius: 0.5rem; margin-top: 1rem;">
                                <p style="color: #64748b; font-size: 0.9rem;">Welcome to the 3D Brain Tumor Segmentation Platform!</p>
                                <p style="color: #64748b; font-size: 0.9rem; margin-top: 0.5rem;">Select a module from the navigation above to get started.</p>
                            </div>
                        `;
                        break;
                    case 'matlab-basics':
                        menuContent = `
                            <h3><i class="fas fa-code"></i> MATLAB Topics</h3>
                            <div style="padding: 1rem; background: #f8fafc; border-radius: 0.5rem; margin-top: 1rem;">
                                <p style="color: #64748b; font-size: 0.9rem;">Learn MATLAB programming fundamentals:</p>
                                <ul style="margin-top: 0.5rem; color: #64748b; font-size: 0.9rem;">
                                    <li>Variables & Operations</li>
                                    <li>Arrays & Matrices</li>
                                    <li>Functions & Plotting</li>
                                    <li>Image Processing</li>
                                </ul>
                            </div>
                        `;
                        break;
                    case 'dicom-processing':
                        menuContent = `
                            <h3><i class="fas fa-file-medical"></i> DICOM Tools</h3>
                            <div style="padding: 1rem; background: #f8fafc; border-radius: 0.5rem; margin-top: 1rem;">
                                <p style="color: #64748b; font-size: 0.9rem;">Medical image processing tools:</p>
                                <ul style="margin-top: 0.5rem; color: #64748b; font-size: 0.9rem;">
                                    <li>DICOM File Handling</li>
                                    <li>Image Viewing & Navigation</li>
                                    <li>Windowing & Leveling</li>
                                    <li>Image Analysis</li>
                                </ul>
                            </div>
                        `;
                        break;
                    case '3d-segmentation':
                        menuContent = `
                            <h3><i class="fas fa-cube"></i> 3D Tools</h3>
                            <div style="padding: 1rem; background: #f8fafc; border-radius: 0.5rem; margin-top: 1rem;">
                                <p style="color: #64748b; font-size: 0.9rem;">3D visualization and segmentation:</p>
                                <ul style="margin-top: 0.5rem; color: #64748b; font-size: 0.9rem;">
                                    <li>3D Brain Visualization</li>
                                    <li>Tumor Segmentation</li>
                                    <li>Volume Measurements</li>
                                    <li>Interactive Analysis</li>
                                </ul>
                            </div>
                        `;
                        break;
                    case 'exercises':
                        menuContent = `
                            <h3><i class="fas fa-tasks"></i> Exercise Categories</h3>
                            <div style="padding: 1rem; background: #f8fafc; border-radius: 0.5rem; margin-top: 1rem;">
                                <p style="color: #64748b; font-size: 0.9rem;">Practice with hands-on exercises:</p>
                                <ul style="margin-top: 0.5rem; color: #64748b; font-size: 0.9rem;">
                                    <li>MATLAB Fundamentals</li>
                                    <li>Image Processing</li>
                                    <li>3D Visualization</li>
                                    <li>Advanced Topics</li>
                                </ul>
                            </div>
                        `;
                        break;
                    default:
                        menuContent = '<p>Select a section to see available options.</p>';
                }

                sectionMenu.innerHTML = menuContent;
            }

            setupInteractions() {
                // Tool buttons
                const toolButtons = document.querySelectorAll('.tool-btn');
                toolButtons.forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        const action = e.target.id || e.target.closest('.tool-btn').id;
                        this.handleToolAction(action);
                    });
                });

                // MATLAB code editor buttons
                const runBtn = document.getElementById('run-matlab-code');
                if (runBtn) {
                    runBtn.addEventListener('click', () => this.runMATLABCode());
                }

                const clearBtn = document.getElementById('clear-matlab-code');
                if (clearBtn) {
                    clearBtn.addEventListener('click', () => this.clearMATLABCode());
                }

                const clearOutputBtn = document.getElementById('clear-output');
                if (clearOutputBtn) {
                    clearOutputBtn.addEventListener('click', () => this.clearMATLABOutput());
                }

                // Modal close
                const modalClose = document.querySelector('.modal-close');
                if (modalClose) {
                    modalClose.addEventListener('click', () => {
                        const modal = document.getElementById('exercise-modal');
                        if (modal) modal.style.display = 'none';
                    });
                }

                // DICOM controls
                this.setupDICOMControls();

                // 3D controls
                this.setup3DControls();

                // Exercise items
                const exerciseItems = document.querySelectorAll('.exercise-item');
                exerciseItems.forEach(item => {
                    item.addEventListener('click', (e) => {
                        const exerciseId = e.currentTarget.dataset.exercise;
                        this.loadExercise(exerciseId);
                    });
                });
            }

            loadExercise(exerciseId) {
                console.log(`Loading exercise: ${exerciseId}`);

                const exerciseContent = document.getElementById('exercise-content');
                if (!exerciseContent) return;

                const exercises = {
                    'matlab-1': {
                        title: 'Variables and Arrays',
                        description: 'Learn basic variable operations and array manipulation',
                        instructions: `
                            <h3>Exercise: Variables and Arrays</h3>
                            <p>Complete the following tasks in MATLAB:</p>
                            <ol>
                                <li>Create a variable 'x' with value 10</li>
                                <li>Create a variable 'y' with value 5</li>
                                <li>Calculate the sum and store in 'result'</li>
                                <li>Create an array 'numbers' with values [1, 2, 3, 4, 5]</li>
                                <li>Display the result using disp()</li>
                            </ol>
                        `,
                        starterCode: `% Exercise 1: Variables and Arrays
% Complete the tasks below

% Task 1: Create variable x
x = ;

% Task 2: Create variable y
y = ;

% Task 3: Calculate sum
result = ;

% Task 4: Create array
numbers = ;

% Task 5: Display result
disp();`
                    },
                    'matlab-2': {
                        title: 'Matrix Operations',
                        description: 'Practice matrix multiplication and transformations',
                        instructions: `
                            <h3>Exercise: Matrix Operations</h3>
                            <p>Work with matrices in MATLAB:</p>
                            <ol>
                                <li>Create a 2x3 matrix A with values [[1,2,3], [4,5,6]]</li>
                                <li>Create a 3x2 matrix B with values [[1,2], [3,4], [5,6]]</li>
                                <li>Multiply A and B to get matrix C</li>
                                <li>Display all matrices</li>
                            </ol>
                        `,
                        starterCode: `% Exercise 2: Matrix Operations
% Complete the matrix operations below

% Task 1: Create matrix A (2x3)
A = ;

% Task 2: Create matrix B (3x2)
B = ;

% Task 3: Multiply A and B
C = ;

% Display results
disp('Matrix A:');
disp(A);
disp('Matrix B:');
disp(B);
disp('Matrix C (A*B):');
disp(C);`
                    },
                    'image-1': {
                        title: 'Image Filtering',
                        description: 'Apply filters to medical images',
                        instructions: `
                            <h3>Exercise: Image Filtering</h3>
                            <p>Learn basic image filtering techniques:</p>
                            <ol>
                                <li>Create a sample image matrix</li>
                                <li>Apply basic filtering operations</li>
                                <li>Display the results</li>
                            </ol>
                        `,
                        starterCode: `% Exercise: Image Filtering
% Create and process a sample image

% Task 1: Create sample image
image = rand(100, 100);

% Task 2: Apply simple filter
filtered = image;

% Task 3: Display results
disp('Image processing complete');
disp(['Image size: ', num2str(size(image))]);`
                    },
                    'image-2': {
                        title: 'Tumor Detection',
                        description: 'Implement basic tumor detection algorithms',
                        instructions: `
                            <h3>Exercise: Tumor Detection</h3>
                            <p>Implement a basic tumor detection algorithm:</p>
                            <ol>
                                <li>Create a simulated brain image</li>
                                <li>Add a tumor region</li>
                                <li>Detect the tumor using thresholding</li>
                                <li>Calculate tumor properties</li>
                            </ol>
                        `,
                        starterCode: `% Exercise: Tumor Detection
% Implement basic tumor detection

% Task 1: Create brain image
brain = zeros(200, 200);

% Task 2: Add tumor
tumor_center = [100, 100];
tumor_size = 20;

% Task 3: Detection algorithm
threshold = 0.5;

% Task 4: Calculate properties
disp('Tumor detection algorithm implemented');`
                    }
                };

                const exercise = exercises[exerciseId];
                if (!exercise) {
                    exerciseContent.innerHTML = '<p>Exercise not found.</p>';
                    return;
                }

                exerciseContent.innerHTML = `
                    <div class="exercise-header">
                        <h2>${exercise.title}</h2>
                        <div class="exercise-meta">
                            <span class="difficulty easy">INTERACTIVE</span>
                        </div>
                    </div>

                    <div class="exercise-instructions">
                        ${exercise.instructions}
                    </div>

                    <div class="exercise-workspace">
                        <div class="code-section">
                            <div class="code-header">
                                <h4>Your Code</h4>
                                <div class="code-actions">
                                    <button class="btn-run" onclick="window.app.runExerciseCode()">
                                        <i class="fas fa-play"></i> Run
                                    </button>
                                    <button class="btn-clear" onclick="window.app.clearExerciseCode()">
                                        <i class="fas fa-undo"></i> Reset
                                    </button>
                                </div>
                            </div>
                            <textarea id="exercise-code-editor" class="exercise-code-editor">${exercise.starterCode}</textarea>
                        </div>

                        <div class="output-section">
                            <div class="output-header">
                                <h4>Output</h4>
                                <button class="btn-clear-output" onclick="window.app.clearExerciseOutput()">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                            <div id="exercise-output" class="exercise-output"></div>
                        </div>
                    </div>

                    <div class="exercise-actions">
                        <button class="btn-hint" onclick="window.app.showExerciseHint('${exerciseId}')">
                            <i class="fas fa-lightbulb"></i> Hint
                        </button>
                        <button class="btn-solution" onclick="window.app.showExerciseSolution('${exerciseId}')">
                            <i class="fas fa-eye"></i> Show Solution
                        </button>
                        <button class="btn-submit" onclick="window.app.submitExercise('${exerciseId}')">
                            <i class="fas fa-paper-plane"></i> Submit
                        </button>
                    </div>
                `;

                this.showNotification(`Loaded exercise: ${exercise.title}`, 'success');
            }

            runExerciseCode() {
                const editor = document.getElementById('exercise-code-editor');
                const output = document.getElementById('exercise-output');

                if (!editor || !output) return;

                const code = editor.value.trim();
                if (!code) {
                    this.addExerciseOutput('No code to execute', 'warning');
                    return;
                }

                this.addExerciseOutput('>> Running exercise code...', 'info');

                try {
                    const result = this.simulateMATLAB(code);
                    if (result) {
                        this.addExerciseOutput(result, 'success');
                    }
                } catch (error) {
                    this.addExerciseOutput(`Error: ${error.message}`, 'error');
                }
            }

            clearExerciseCode() {
                const editor = document.getElementById('exercise-code-editor');
                if (editor) {
                    // Reset to starter code - in a real implementation, you'd store the original
                    editor.value = editor.defaultValue || '';
                }
            }

            clearExerciseOutput() {
                const output = document.getElementById('exercise-output');
                if (output) output.innerHTML = '';
            }

            addExerciseOutput(text, type = 'normal') {
                const output = document.getElementById('exercise-output');
                if (!output) return;

                const outputLine = document.createElement('div');
                outputLine.className = `output-line ${type}`;
                outputLine.textContent = text;

                switch (type) {
                    case 'error':
                        outputLine.style.color = '#ef4444';
                        break;
                    case 'warning':
                        outputLine.style.color = '#f97316';
                        break;
                    case 'success':
                        outputLine.style.color = '#10b981';
                        break;
                    case 'info':
                        outputLine.style.color = '#3b82f6';
                        break;
                    default:
                        outputLine.style.color = '#e2e8f0';
                }

                output.appendChild(outputLine);
                output.scrollTop = output.scrollHeight;
            }

            showExerciseHint(exerciseId) {
                const hints = {
                    'matlab-1': 'Remember: Use semicolons to suppress output, and square brackets [] to create arrays.',
                    'matlab-2': 'Matrix multiplication in MATLAB uses the * operator. Make sure dimensions are compatible!',
                    'image-1': 'Image processing often involves element-wise operations and filtering.',
                    'image-2': 'Thresholding is a simple way to segment images. Try using logical indexing.'
                };

                const hint = hints[exerciseId] || 'Break down the problem into smaller steps and test each part.';
                this.addExerciseOutput(`💡 Hint: ${hint}`, 'info');
            }

            showExerciseSolution(exerciseId) {
                const solutions = {
                    'matlab-1': `% Solution: Variables and Arrays
x = 10;
y = 5;
result = x + y;
numbers = [1, 2, 3, 4, 5];
disp(['Result: ', num2str(result)]);
disp(['Numbers: ', num2str(numbers)]);`,
                    'matlab-2': `% Solution: Matrix Operations
A = [1, 2, 3; 4, 5, 6];
B = [1, 2; 3, 4; 5, 6];
C = A * B;
disp('Matrix A:');
disp(A);
disp('Matrix B:');
disp(B);
disp('Matrix C (A*B):');
disp(C);`,
                    'image-1': `% Solution: Image Filtering
image = rand(100, 100);
filtered = image * 0.8; % Simple brightness adjustment
disp('Image processing complete');
disp(['Original image size: ', num2str(size(image))]);
disp(['Filtered image size: ', num2str(size(filtered))]);`,
                    'image-2': `% Solution: Tumor Detection
brain = zeros(200, 200);
tumor_center = [100, 100];
tumor_size = 20;
% Add tumor to brain
for i = 1:200
    for j = 1:200
        dist = sqrt((i-tumor_center(1))^2 + (j-tumor_center(2))^2);
        if dist < tumor_size
            brain(i,j) = 1;
        end
    end
end
threshold = 0.5;
detected = brain > threshold;
tumor_pixels = sum(detected(:));
disp(['Tumor detection complete']);
disp(['Detected tumor pixels: ', num2str(tumor_pixels)]);`
                };

                const solution = solutions[exerciseId];
                if (solution) {
                    const editor = document.getElementById('exercise-code-editor');
                    if (editor) {
                        editor.value = solution;
                        this.addExerciseOutput('Solution loaded! Study it carefully.', 'info');
                    }
                } else {
                    this.addExerciseOutput('Solution not available for this exercise.', 'warning');
                }
            }

            submitExercise(exerciseId) {
                this.addExerciseOutput('🎉 Exercise submitted! Great work!', 'success');
                this.showNotification('Exercise completed successfully!', 'success');

                // Mark exercise as completed
                const exerciseItem = document.querySelector(`[data-exercise="${exerciseId}"]`);
                if (exerciseItem) {
                    const statusIcon = exerciseItem.querySelector('.exercise-status i');
                    if (statusIcon) {
                        statusIcon.className = 'fas fa-check-circle';
                        statusIcon.style.color = '#10b981';
                    }
                }
            }

            runMATLABCode() {
                const editor = document.getElementById('matlab-editor');
                const output = document.getElementById('matlab-output');

                if (!editor || !output) return;

                const code = editor.value.trim();
                if (!code) {
                    this.addMATLABOutput('No code to execute', 'warning');
                    return;
                }

                this.addMATLABOutput('>> Running MATLAB code...', 'info');

                try {
                    // Simple MATLAB simulation
                    const result = this.simulateMATLAB(code);
                    if (result) {
                        this.addMATLABOutput(result, 'success');
                    }
                } catch (error) {
                    this.addMATLABOutput(`Error: ${error.message}`, 'error');
                }
            }

            simulateMATLAB(code) {
                const lines = code.split('\n').filter(line => {
                    const trimmed = line.trim();
                    return trimmed && !trimmed.startsWith('%');
                });

                let output = [];
                let variables = {};

                for (const line of lines) {
                    try {
                        const result = this.executeMATLABLine(line.trim(), variables);
                        if (result !== null && result !== undefined) {
                            output.push(result);
                        }
                    } catch (error) {
                        output.push(`Error in line "${line}": ${error.message}`);
                    }
                }

                return output.join('\n');
            }

            executeMATLABLine(line, variables) {
                // Handle variable assignments
                if (line.includes('=') && !line.includes('==')) {
                    const [varName, expression] = line.split('=').map(s => s.trim());
                    const cleanVarName = varName.replace(';', '');

                    // Simple expression evaluation
                    let value;
                    if (expression.match(/^\d+$/)) {
                        value = parseInt(expression);
                    } else if (expression.match(/^\d+\.\d+$/)) {
                        value = parseFloat(expression);
                    } else if (expression.match(/^\[.*\]$/)) {
                        const arrayStr = expression.slice(1, -1);
                        value = arrayStr.split(',').map(s => parseFloat(s.trim()));
                    } else if (expression.includes('+') || expression.includes('-') || expression.includes('*') || expression.includes('/')) {
                        // Simple arithmetic
                        let expr = expression;
                        Object.keys(variables).forEach(varName => {
                            expr = expr.replace(new RegExp(`\\b${varName}\\b`, 'g'), variables[varName]);
                        });
                        try {
                            value = eval(expr);
                        } catch (e) {
                            value = expression;
                        }
                    } else {
                        value = expression;
                    }

                    variables[cleanVarName] = value;

                    if (!line.endsWith(';')) {
                        return `${cleanVarName} = ${Array.isArray(value) ? `[${value.join(', ')}]` : value}`;
                    }
                    return null;
                }

                // Handle disp function
                if (line.startsWith('disp(')) {
                    const content = line.slice(5, -1);
                    if (content.startsWith("'") && content.endsWith("'")) {
                        return content.slice(1, -1);
                    } else if (content.startsWith('[') && content.endsWith(']')) {
                        // Handle string concatenation
                        let result = content.slice(1, -1);
                        Object.keys(variables).forEach(varName => {
                            result = result.replace(new RegExp(`\\b${varName}\\b`, 'g'), variables[varName]);
                        });
                        result = result.replace(/'/g, '').replace(/,\s*/g, '');
                        return result;
                    } else if (variables[content]) {
                        return Array.isArray(variables[content]) ? `[${variables[content].join(', ')}]` : variables[content];
                    }
                    return content;
                }

                // Handle plot function
                if (line.startsWith('plot(')) {
                    return 'Plot created successfully (visualization not available in this demo)';
                }

                // Handle title function
                if (line.startsWith('title(')) {
                    const title = line.slice(6, -1).replace(/'/g, '');
                    return `Plot title set: ${title}`;
                }

                return null;
            }

            addMATLABOutput(text, type = 'normal') {
                const output = document.getElementById('matlab-output');
                if (!output) return;

                const outputLine = document.createElement('div');
                outputLine.className = `output-line ${type}`;
                outputLine.textContent = text;

                switch (type) {
                    case 'error':
                        outputLine.style.color = '#ef4444';
                        break;
                    case 'warning':
                        outputLine.style.color = '#f97316';
                        break;
                    case 'success':
                        outputLine.style.color = '#10b981';
                        break;
                    case 'info':
                        outputLine.style.color = '#3b82f6';
                        break;
                    default:
                        outputLine.style.color = '#e2e8f0';
                }

                output.appendChild(outputLine);
                output.scrollTop = output.scrollHeight;
            }

            clearMATLABCode() {
                const editor = document.getElementById('matlab-editor');
                if (editor) editor.value = '';
            }

            clearMATLABOutput() {
                const output = document.getElementById('matlab-output');
                if (output) output.innerHTML = '';
            }

            setupDICOMControls() {
                // Add basic DICOM functionality when in DICOM section
                const fileInput = document.getElementById('dicom-file-input');
                if (fileInput) {
                    fileInput.addEventListener('change', (e) => {
                        if (e.target.files.length > 0) {
                            this.showNotification(`Selected ${e.target.files.length} DICOM file(s)`, 'info');
                            this.simulateDICOMDisplay();
                        }
                    });
                }

                // Slider controls
                const sliders = ['brightness-slider', 'contrast-slider', 'window-level-slider', 'window-width-slider'];
                sliders.forEach(sliderId => {
                    const slider = document.getElementById(sliderId);
                    if (slider) {
                        slider.addEventListener('input', (e) => {
                            this.updateDICOMDisplay(sliderId, e.target.value);
                        });
                    }
                });
            }

            simulateDICOMDisplay() {
                const canvas = document.getElementById('dicom-canvas');
                if (!canvas) return;

                const ctx = canvas.getContext('2d');
                canvas.width = 512;
                canvas.height = 512;

                // Create a simple brain-like image
                const imageData = ctx.createImageData(512, 512);
                const data = imageData.data;

                for (let i = 0; i < data.length; i += 4) {
                    const x = (i / 4) % 512;
                    const y = Math.floor((i / 4) / 512);
                    const centerX = 256;
                    const centerY = 256;
                    const distance = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);

                    let intensity = 0;
                    if (distance < 200) {
                        intensity = 150 + Math.sin(distance * 0.1) * 50;
                        intensity += (Math.random() - 0.5) * 30;
                    }

                    intensity = Math.max(0, Math.min(255, intensity));

                    data[i] = intensity;
                    data[i + 1] = intensity;
                    data[i + 2] = intensity;
                    data[i + 3] = 255;
                }

                ctx.putImageData(imageData, 0, 0);
                this.showNotification('Sample brain image displayed', 'success');
            }

            updateDICOMDisplay(controlId, value) {
                console.log(`DICOM control ${controlId} changed to ${value}`);
                // In a real implementation, this would update the image display
            }

            setup3DControls() {
                // Add basic 3D functionality
                const container = document.getElementById('three-js-container');
                if (container && this.currentSection === '3d-segmentation') {
                    this.init3DViewer();
                }

                // Opacity controls
                const brainOpacity = document.getElementById('brain-opacity');
                const tumorOpacity = document.getElementById('tumor-opacity');

                if (brainOpacity) {
                    brainOpacity.addEventListener('input', (e) => {
                        console.log(`Brain opacity: ${e.target.value}%`);
                    });
                }

                if (tumorOpacity) {
                    tumorOpacity.addEventListener('input', (e) => {
                        console.log(`Tumor opacity: ${e.target.value}%`);
                    });
                }
            }

            init3DViewer() {
                const container = document.getElementById('three-js-container');
                if (!container || !window.THREE) return;

                try {
                    // Create a simple 3D scene
                    const scene = new THREE.Scene();
                    const camera = new THREE.PerspectiveCamera(75, container.clientWidth / container.clientHeight, 0.1, 1000);
                    const renderer = new THREE.WebGLRenderer();

                    renderer.setSize(container.clientWidth, container.clientHeight);
                    container.appendChild(renderer.domElement);

                    // Create a simple brain-like sphere
                    const geometry = new THREE.SphereGeometry(2, 32, 16);
                    const material = new THREE.MeshPhongMaterial({ color: 0xffc0cb, transparent: true, opacity: 0.8 });
                    const brain = new THREE.Mesh(geometry, material);
                    scene.add(brain);

                    // Create a tumor
                    const tumorGeometry = new THREE.SphereGeometry(0.5, 16, 12);
                    const tumorMaterial = new THREE.MeshPhongMaterial({ color: 0xff4444, transparent: true, opacity: 0.9 });
                    const tumor = new THREE.Mesh(tumorGeometry, tumorMaterial);
                    tumor.position.set(1, 0.5, -0.5);
                    scene.add(tumor);

                    // Add lights
                    const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
                    scene.add(ambientLight);

                    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
                    directionalLight.position.set(10, 10, 5);
                    scene.add(directionalLight);

                    camera.position.z = 5;

                    // Animation loop
                    const animate = () => {
                        requestAnimationFrame(animate);
                        brain.rotation.y += 0.01;
                        tumor.rotation.y += 0.01;
                        renderer.render(scene, camera);
                    };

                    animate();
                    this.showNotification('3D brain model loaded successfully!', 'success');

                } catch (error) {
                    console.error('3D viewer error:', error);
                    container.innerHTML = `
                        <div style="display: flex; align-items: center; justify-content: center; height: 100%; background: #f8fafc; border-radius: 0.5rem;">
                            <div style="text-align: center; color: #64748b;">
                                <i class="fas fa-cube" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                                <p>3D Viewer</p>
                                <p style="font-size: 0.875rem;">WebGL not available or Three.js not loaded</p>
                            </div>
                        </div>
                    `;
                }
            }

            handleToolAction(action) {
                console.log(`Tool action: ${action}`);

                switch (action) {
                    case 'reset-view':
                        this.showNotification('View reset to default position', 'info');
                        break;
                    case 'toggle-wireframe':
                        this.showNotification('Wireframe mode toggled', 'info');
                        break;
                    case 'export-model':
                        this.showNotification('Model export feature coming soon!', 'info');
                        break;
                }
            }

            showNotification(message, type = 'info') {
                const notification = document.createElement('div');
                notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    padding: 12px 20px;
                    border-radius: 6px;
                    color: white;
                    z-index: 1000;
                    animation: slideIn 0.3s ease-out;
                    max-width: 300px;
                `;

                switch (type) {
                    case 'success':
                        notification.style.backgroundColor = '#10b981';
                        break;
                    case 'error':
                        notification.style.backgroundColor = '#ef4444';
                        break;
                    case 'warning':
                        notification.style.backgroundColor = '#f97316';
                        break;
                    default:
                        notification.style.backgroundColor = '#3b82f6';
                }

                notification.textContent = message;
                document.body.appendChild(notification);

                setTimeout(() => {
                    notification.remove();
                }, 3000);
            }
        }

        // Initialize the application when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            window.app = new SimpleBrainApp();
            console.log('🧠 3D Brain Tumor Segmentation Platform loaded successfully!');
        });

        // Add some CSS animations
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }

            .tool-btn {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                width: 100%;
                padding: 0.5rem 1rem;
                border: 1px solid var(--bg-tertiary);
                background: var(--bg-primary);
                color: var(--text-secondary);
                border-radius: 0.5rem;
                cursor: pointer;
                transition: all 0.15s ease-in-out;
                margin-bottom: 0.5rem;
                font-size: 0.875rem;
            }

            .tool-btn:hover {
                background: var(--primary-color);
                color: white;
                border-color: var(--primary-color);
            }

            .tools-panel h3 {
                font-size: 1rem;
                font-weight: 600;
                color: var(--text-primary);
                margin-bottom: 1rem;
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }

            .progress-circle {
                position: relative;
                width: 50px;
                height: 50px;
                border-radius: 50%;
                background: conic-gradient(var(--primary-color) 0deg, var(--bg-tertiary) 0deg);
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .progress-text {
                font-size: 0.75rem;
                font-weight: 600;
                color: var(--text-primary);
            }

            .modal {
                display: none;
                position: fixed;
                z-index: 1000;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.5);
            }

            .modal-content {
                background: white;
                margin: 15% auto;
                padding: 20px;
                border-radius: 8px;
                width: 80%;
                max-width: 600px;
            }

            .modal-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 20px;
            }

            .modal-close {
                background: none;
                border: none;
                font-size: 24px;
                cursor: pointer;
                color: #999;
            }

            .modal-close:hover {
                color: #333;
            }

            /* Additional styles for better layout */
            .split-layout {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 2rem;
                height: calc(100vh - 250px);
            }

            .tutorial-panel {
                background: var(--bg-primary);
                border: 1px solid var(--bg-tertiary);
                border-radius: var(--radius-lg);
                overflow: hidden;
            }

            .tutorial-content {
                padding: 1.5rem;
                height: 100%;
                overflow-y: auto;
            }

            .code-panel {
                display: flex;
                flex-direction: column;
                gap: 1rem;
            }

            .code-editor-container {
                flex: 1;
                background: var(--bg-primary);
                border: 1px solid var(--bg-tertiary);
                border-radius: var(--radius-lg);
                overflow: hidden;
            }

            .editor-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 1rem;
                background: var(--bg-secondary);
                border-bottom: 1px solid var(--bg-tertiary);
            }

            .editor-title {
                font-weight: 600;
                color: var(--text-primary);
            }

            .editor-controls {
                display: flex;
                gap: 0.5rem;
            }

            .btn-run, .btn-clear {
                display: flex;
                align-items: center;
                gap: 0.25rem;
                padding: 0.5rem 1rem;
                border: none;
                border-radius: 0.375rem;
                font-size: 0.875rem;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.15s ease-in-out;
            }

            .btn-run {
                background: var(--secondary-color);
                color: white;
            }

            .btn-run:hover {
                background: #059669;
            }

            .btn-clear {
                background: #ef4444;
                color: white;
            }

            .btn-clear:hover {
                background: #dc2626;
            }

            .code-editor {
                width: 100%;
                height: 300px;
                border: none;
                outline: none;
                font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                font-size: 14px;
                line-height: 1.5;
                padding: 1rem;
                background: #2d3748;
                color: #e2e8f0;
                resize: none;
            }

            .output-container {
                height: 200px;
                background: var(--bg-primary);
                border: 1px solid var(--bg-tertiary);
                border-radius: var(--radius-lg);
                overflow: hidden;
            }

            .output-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 1rem;
                background: var(--bg-secondary);
                border-bottom: 1px solid var(--bg-tertiary);
            }

            .output-title {
                font-weight: 600;
                color: var(--text-primary);
            }

            .btn-clear-output {
                background: none;
                border: none;
                color: var(--text-secondary);
                cursor: pointer;
                padding: 0.25rem;
                border-radius: 0.25rem;
                transition: all 0.15s ease-in-out;
            }

            .btn-clear-output:hover {
                background: var(--bg-tertiary);
                color: var(--text-primary);
            }

            .code-output {
                padding: 1rem;
                height: calc(100% - 60px);
                overflow-y: auto;
                font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                font-size: 14px;
                line-height: 1.5;
                background: #1a202c;
                color: #e2e8f0;
            }

            .output-line {
                margin-bottom: 0.25rem;
            }

            /* DICOM Layout */
            .dicom-layout {
                display: grid;
                grid-template-columns: 300px 1fr;
                gap: 2rem;
                height: calc(100vh - 250px);
            }

            .dicom-controls {
                background: var(--bg-primary);
                border: 1px solid var(--bg-tertiary);
                border-radius: var(--radius-lg);
                padding: 1.5rem;
                overflow-y: auto;
            }

            .file-upload-area {
                margin-bottom: 2rem;
            }

            .upload-label {
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 1rem;
                padding: 2rem;
                border: 2px dashed var(--bg-tertiary);
                border-radius: var(--radius-lg);
                cursor: pointer;
                transition: all 0.15s ease-in-out;
                text-align: center;
            }

            .upload-label:hover {
                border-color: var(--primary-color);
                background: var(--bg-secondary);
            }

            .upload-label i {
                font-size: 2rem;
                color: var(--primary-color);
            }

            #dicom-file-input {
                display: none;
            }

            .processing-controls h3 {
                font-size: 1rem;
                font-weight: 600;
                color: var(--text-primary);
                margin-bottom: 1rem;
            }

            .control-group {
                margin-bottom: 1rem;
            }

            .control-group label {
                display: block;
                font-size: 0.875rem;
                font-weight: 500;
                color: var(--text-secondary);
                margin-bottom: 0.5rem;
            }

            .control-group input[type="range"] {
                width: 100%;
                height: 6px;
                border-radius: 3px;
                background: var(--bg-tertiary);
                outline: none;
                cursor: pointer;
            }

            .dicom-viewer {
                background: var(--bg-primary);
                border: 1px solid var(--bg-tertiary);
                border-radius: var(--radius-lg);
                overflow: hidden;
            }

            .viewer-container {
                position: relative;
                height: calc(100% - 60px);
            }

            .dicom-canvas {
                width: 100%;
                height: 100%;
                object-fit: contain;
                background: #000;
            }

            .viewer-info {
                position: absolute;
                bottom: 0;
                left: 0;
                right: 0;
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 1rem;
            }

            .slice-info {
                display: flex;
                align-items: center;
                gap: 1rem;
            }

            #slice-slider {
                flex: 1;
            }

            /* 3D Segmentation Layout */
            .segmentation-layout {
                display: grid;
                grid-template-columns: 300px 1fr;
                gap: 2rem;
                height: calc(100vh - 250px);
            }

            .segmentation-controls {
                background: var(--bg-primary);
                border: 1px solid var(--bg-tertiary);
                border-radius: var(--radius-lg);
                padding: 1.5rem;
                overflow-y: auto;
                display: flex;
                flex-direction: column;
                gap: 2rem;
            }

            .model-controls h3,
            .segmentation-tools h3,
            .analysis-results h3 {
                font-size: 1rem;
                font-weight: 600;
                color: var(--text-primary);
                margin-bottom: 1rem;
            }

            .viewer-3d {
                background: var(--bg-primary);
                border: 1px solid var(--bg-tertiary);
                border-radius: var(--radius-lg);
                overflow: hidden;
                position: relative;
            }

            .three-js-container {
                width: 100%;
                height: calc(100% - 60px);
                background: linear-gradient(135deg, #1e293b, #334155);
            }

            .viewer-controls {
                display: flex;
                justify-content: center;
                gap: 0.5rem;
                padding: 1rem;
                background: var(--bg-secondary);
                border-top: 1px solid var(--bg-tertiary);
            }

            .view-btn {
                padding: 0.5rem 1rem;
                border: 1px solid var(--bg-tertiary);
                background: var(--bg-primary);
                color: var(--text-secondary);
                border-radius: 0.375rem;
                cursor: pointer;
                transition: all 0.15s ease-in-out;
                font-size: 0.875rem;
            }

            .view-btn:hover,
            .view-btn.active {
                background: var(--primary-color);
                color: white;
                border-color: var(--primary-color);
            }

            /* Responsive Design */
            @media (max-width: 1024px) {
                .split-layout,
                .dicom-layout,
                .segmentation-layout {
                    grid-template-columns: 1fr;
                    height: auto;
                }

                .main-content {
                    flex-direction: column;
                }

                .sidebar {
                    width: 100%;
                    height: auto;
                    border-right: none;
                    border-bottom: 1px solid var(--bg-tertiary);
                }
            }

            @media (max-width: 768px) {
                .code-editor {
                    height: 200px;
                }

                .output-container {
                    height: 150px;
                }

                .header-content {
                    flex-direction: column;
                    gap: 1rem;
                    padding: 1rem;
                }

                .main-nav {
                    flex-wrap: wrap;
                    justify-content: center;
                }

                .overview-grid {
                    grid-template-columns: 1fr;
                }
            }

            /* Exercise Interface Styles */
            .exercise-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 1.5rem;
                padding-bottom: 1rem;
                border-bottom: 2px solid var(--bg-tertiary);
            }

            .exercise-meta {
                display: flex;
                gap: 1rem;
                align-items: center;
            }

            .difficulty {
                font-size: 0.75rem;
                padding: 0.25rem 0.75rem;
                border-radius: 1rem;
                font-weight: 500;
                text-transform: uppercase;
            }

            .difficulty.easy {
                background: #dcfce7;
                color: #166534;
            }

            .exercise-instructions {
                background: var(--bg-secondary);
                padding: 1.5rem;
                border-radius: var(--radius-lg);
                margin-bottom: 2rem;
            }

            .exercise-workspace {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 1.5rem;
                margin-bottom: 2rem;
            }

            .code-section,
            .output-section {
                background: var(--bg-primary);
                border: 1px solid var(--bg-tertiary);
                border-radius: var(--radius-lg);
                overflow: hidden;
            }

            .code-header,
            .output-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 1rem;
                background: var(--bg-secondary);
                border-bottom: 1px solid var(--bg-tertiary);
            }

            .code-actions {
                display: flex;
                gap: 0.5rem;
            }

            .exercise-code-editor {
                width: 100%;
                height: 300px;
                font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                font-size: 14px;
                border: none;
                outline: none;
                padding: 1rem;
                background: #2d3748;
                color: #e2e8f0;
                resize: none;
            }

            .exercise-output {
                height: 300px;
                overflow-y: auto;
                background: #1a202c;
                color: #e2e8f0;
                padding: 1rem;
                font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                font-size: 14px;
            }

            .exercise-actions {
                display: flex;
                justify-content: center;
                gap: 1rem;
            }

            .btn-hint,
            .btn-solution,
            .btn-submit {
                padding: 0.75rem 1.5rem;
                border: none;
                border-radius: var(--radius-md);
                font-weight: 500;
                cursor: pointer;
                transition: all var(--transition-fast);
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }

            .btn-hint {
                background: var(--accent-color);
                color: white;
            }

            .btn-hint:hover {
                background: #d97706;
            }

            .btn-solution {
                background: var(--text-secondary);
                color: white;
            }

            .btn-solution:hover {
                background: #475569;
            }

            .btn-submit {
                background: var(--secondary-color);
                color: white;
            }

            .btn-submit:hover {
                background: #059669;
            }

            /* Responsive exercise layout */
            @media (max-width: 1024px) {
                .exercise-workspace {
                    grid-template-columns: 1fr;
                }

                .exercise-header {
                    flex-direction: column;
                    align-items: flex-start;
                    gap: 1rem;
                }

                .exercise-actions {
                    flex-direction: column;
                    align-items: stretch;
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>

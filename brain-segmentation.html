<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D Brain Tumor Segmentation - Interactive Learning Platform</title>
    <meta name="description" content="Interactive platform for learning MATLAB programming and medical image processing with 3D brain tumor segmentation">
    
    <!-- External Libraries -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dat-gui/0.7.9/dat.gui.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/octave/octave.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/plotly.js/2.18.0/plotly.min.js"></script>
    
    <!-- CodeMirror CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/theme/monokai.min.css">
    
    <!-- Custom Styles -->
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/components.css">
    <link rel="stylesheet" href="styles/3d-viewer.css">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-content">
            <div class="brain-loader">
                <div class="brain-hemisphere left"></div>
                <div class="brain-hemisphere right"></div>
            </div>
            <h2>Loading 3D Brain Tumor Segmentation Platform</h2>
            <div class="loading-bar">
                <div class="loading-progress"></div>
            </div>
        </div>
    </div>

    <!-- Main Application -->
    <div id="app" class="app-container" style="display: none;">
        <!-- Header -->
        <header class="app-header">
            <div class="header-content">
                <div class="logo-section">
                    <i class="fas fa-brain"></i>
                    <h1>3D Brain Tumor Segmentation</h1>
                    <span class="subtitle">Interactive Learning Platform</span>
                </div>
                <nav class="main-nav">
                    <button class="nav-btn active" data-section="overview">
                        <i class="fas fa-home"></i> Overview
                    </button>
                    <button class="nav-btn" data-section="matlab-basics">
                        <i class="fas fa-code"></i> MATLAB Basics
                    </button>
                    <button class="nav-btn" data-section="dicom-processing">
                        <i class="fas fa-file-medical"></i> DICOM Processing
                    </button>
                    <button class="nav-btn" data-section="3d-segmentation">
                        <i class="fas fa-cube"></i> 3D Segmentation
                    </button>
                    <button class="nav-btn" data-section="exercises">
                        <i class="fas fa-tasks"></i> Exercises
                    </button>
                </nav>
                <div class="user-progress">
                    <div class="progress-circle">
                        <span class="progress-text">0%</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content Area -->
        <main class="main-content">
            <!-- Sidebar -->
            <aside class="sidebar">
                <div class="sidebar-content">
                    <div class="section-menu" id="section-menu">
                        <!-- Dynamic content based on active section -->
                    </div>
                    
                    <div class="tools-panel">
                        <h3><i class="fas fa-tools"></i> Tools</h3>
                        <button class="tool-btn" id="reset-view">
                            <i class="fas fa-undo"></i> Reset View
                        </button>
                        <button class="tool-btn" id="toggle-wireframe">
                            <i class="fas fa-project-diagram"></i> Wireframe
                        </button>
                        <button class="tool-btn" id="export-model">
                            <i class="fas fa-download"></i> Export
                        </button>
                    </div>
                </div>
            </aside>

            <!-- Content Panels -->
            <div class="content-panels">
                <!-- Overview Section -->
                <section id="overview-section" class="content-section active">
                    <div class="section-header">
                        <h2><i class="fas fa-brain"></i> 3D Brain Tumor Segmentation Platform</h2>
                        <p>Learn MATLAB programming and medical image processing through interactive 3D visualization</p>
                    </div>
                    
                    <div class="overview-grid">
                        <div class="feature-card">
                            <div class="card-icon">
                                <i class="fas fa-code"></i>
                            </div>
                            <h3>MATLAB Programming</h3>
                            <p>Master MATLAB fundamentals with interactive coding exercises and real-time feedback</p>
                            <button class="card-btn" data-navigate="matlab-basics">Start Learning</button>
                        </div>
                        
                        <div class="feature-card">
                            <div class="card-icon">
                                <i class="fas fa-file-medical"></i>
                            </div>
                            <h3>DICOM Processing</h3>
                            <p>Learn medical image processing techniques for DICOM files and visualization</p>
                            <button class="card-btn" data-navigate="dicom-processing">Explore DICOM</button>
                        </div>
                        
                        <div class="feature-card">
                            <div class="card-icon">
                                <i class="fas fa-cube"></i>
                            </div>
                            <h3>3D Segmentation</h3>
                            <p>Interactive 3D brain tumor segmentation with advanced visualization tools</p>
                            <button class="card-btn" data-navigate="3d-segmentation">View 3D Model</button>
                        </div>
                        
                        <div class="feature-card">
                            <div class="card-icon">
                                <i class="fas fa-tasks"></i>
                            </div>
                            <h3>Practice Exercises</h3>
                            <p>Hands-on coding challenges and assessments to test your knowledge</p>
                            <button class="card-btn" data-navigate="exercises">Start Practicing</button>
                        </div>
                    </div>
                </section>

                <!-- MATLAB Basics Section -->
                <section id="matlab-basics-section" class="content-section">
                    <div class="section-header">
                        <h2><i class="fas fa-code"></i> MATLAB Programming Fundamentals</h2>
                        <p>Interactive tutorials and coding exercises for MATLAB basics</p>
                    </div>
                    
                    <div class="split-layout">
                        <div class="tutorial-panel">
                            <div class="tutorial-content" id="matlab-tutorial-content">
                                <!-- Dynamic tutorial content -->
                            </div>
                        </div>
                        
                        <div class="code-panel">
                            <div class="code-editor-container">
                                <div class="editor-header">
                                    <span class="editor-title">MATLAB Code Editor</span>
                                    <div class="editor-controls">
                                        <button class="btn-run" id="run-matlab-code">
                                            <i class="fas fa-play"></i> Run
                                        </button>
                                        <button class="btn-clear" id="clear-matlab-code">
                                            <i class="fas fa-trash"></i> Clear
                                        </button>
                                    </div>
                                </div>
                                <textarea id="matlab-editor" class="code-editor"></textarea>
                            </div>
                            
                            <div class="output-container">
                                <div class="output-header">
                                    <span class="output-title">Output</span>
                                    <button class="btn-clear-output" id="clear-output">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                                <div id="matlab-output" class="code-output"></div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- DICOM Processing Section -->
                <section id="dicom-processing-section" class="content-section">
                    <div class="section-header">
                        <h2><i class="fas fa-file-medical"></i> DICOM Image Processing</h2>
                        <p>Learn medical image processing with DICOM format</p>
                    </div>
                    
                    <div class="dicom-layout">
                        <div class="dicom-controls">
                            <div class="file-upload-area">
                                <input type="file" id="dicom-file-input" accept=".dcm,.dicom" multiple>
                                <label for="dicom-file-input" class="upload-label">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                    <span>Upload DICOM Files</span>
                                </label>
                            </div>
                            
                            <div class="processing-controls">
                                <h3>Image Processing</h3>
                                <div class="control-group">
                                    <label>Brightness</label>
                                    <input type="range" id="brightness-slider" min="-100" max="100" value="0">
                                </div>
                                <div class="control-group">
                                    <label>Contrast</label>
                                    <input type="range" id="contrast-slider" min="0" max="200" value="100">
                                </div>
                                <div class="control-group">
                                    <label>Window Level</label>
                                    <input type="range" id="window-level-slider" min="0" max="4096" value="2048">
                                </div>
                                <div class="control-group">
                                    <label>Window Width</label>
                                    <input type="range" id="window-width-slider" min="1" max="4096" value="1024">
                                </div>
                            </div>
                        </div>
                        
                        <div class="dicom-viewer">
                            <div class="viewer-container">
                                <canvas id="dicom-canvas" class="dicom-canvas"></canvas>
                                <div class="viewer-info">
                                    <div class="slice-info">
                                        <span>Slice: <span id="current-slice">1</span> / <span id="total-slices">1</span></span>
                                        <input type="range" id="slice-slider" min="1" max="1" value="1">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 3D Segmentation Section -->
                <section id="3d-segmentation-section" class="content-section">
                    <div class="section-header">
                        <h2><i class="fas fa-cube"></i> 3D Brain Tumor Segmentation</h2>
                        <p>Interactive 3D visualization and tumor segmentation</p>
                    </div>

                    <div class="segmentation-layout">
                        <div class="segmentation-controls">
                            <div class="model-controls">
                                <h3>3D Model Controls</h3>
                                <div class="control-group">
                                    <label>Opacity</label>
                                    <input type="range" id="brain-opacity" min="0" max="100" value="80">
                                </div>
                                <div class="control-group">
                                    <label>Tumor Opacity</label>
                                    <input type="range" id="tumor-opacity" min="0" max="100" value="90">
                                </div>
                                <div class="control-group">
                                    <label>Rotation Speed</label>
                                    <input type="range" id="rotation-speed" min="0" max="10" value="1">
                                </div>
                            </div>

                            <div class="segmentation-tools">
                                <h3>Segmentation Tools</h3>
                                <button class="tool-btn active" data-tool="view">
                                    <i class="fas fa-eye"></i> View
                                </button>
                                <button class="tool-btn" data-tool="segment">
                                    <i class="fas fa-cut"></i> Segment
                                </button>
                                <button class="tool-btn" data-tool="measure">
                                    <i class="fas fa-ruler"></i> Measure
                                </button>
                                <button class="tool-btn" data-tool="annotate">
                                    <i class="fas fa-comment"></i> Annotate
                                </button>
                            </div>

                            <div class="analysis-results">
                                <h3>Analysis Results</h3>
                                <div class="result-item">
                                    <span class="label">Tumor Volume:</span>
                                    <span class="value" id="tumor-volume">-- cm³</span>
                                </div>
                                <div class="result-item">
                                    <span class="label">Brain Volume:</span>
                                    <span class="value" id="brain-volume">-- cm³</span>
                                </div>
                                <div class="result-item">
                                    <span class="label">Tumor Ratio:</span>
                                    <span class="value" id="tumor-ratio">-- %</span>
                                </div>
                            </div>
                        </div>

                        <div class="viewer-3d">
                            <div id="three-js-container" class="three-js-container">
                                <!-- Three.js 3D viewer will be rendered here -->
                            </div>

                            <div class="viewer-controls">
                                <button class="view-btn" data-view="front">Front</button>
                                <button class="view-btn" data-view="side">Side</button>
                                <button class="view-btn" data-view="top">Top</button>
                                <button class="view-btn" data-view="iso">Isometric</button>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Exercises Section -->
                <section id="exercises-section" class="content-section">
                    <div class="section-header">
                        <h2><i class="fas fa-tasks"></i> Interactive Exercises</h2>
                        <p>Practice your skills with hands-on coding challenges</p>
                    </div>

                    <div class="exercises-layout">
                        <div class="exercise-list">
                            <div class="exercise-category">
                                <h3>MATLAB Fundamentals</h3>
                                <div class="exercise-item" data-exercise="matlab-1">
                                    <div class="exercise-info">
                                        <h4>Variables and Arrays</h4>
                                        <p>Learn basic variable operations and array manipulation</p>
                                        <span class="difficulty easy">Easy</span>
                                    </div>
                                    <div class="exercise-status">
                                        <i class="fas fa-circle"></i>
                                    </div>
                                </div>

                                <div class="exercise-item" data-exercise="matlab-2">
                                    <div class="exercise-info">
                                        <h4>Matrix Operations</h4>
                                        <p>Practice matrix multiplication and transformations</p>
                                        <span class="difficulty medium">Medium</span>
                                    </div>
                                    <div class="exercise-status">
                                        <i class="fas fa-circle"></i>
                                    </div>
                                </div>
                            </div>

                            <div class="exercise-category">
                                <h3>Image Processing</h3>
                                <div class="exercise-item" data-exercise="image-1">
                                    <div class="exercise-info">
                                        <h4>Image Filtering</h4>
                                        <p>Apply filters to medical images</p>
                                        <span class="difficulty medium">Medium</span>
                                    </div>
                                    <div class="exercise-status">
                                        <i class="fas fa-circle"></i>
                                    </div>
                                </div>

                                <div class="exercise-item" data-exercise="image-2">
                                    <div class="exercise-info">
                                        <h4>Tumor Detection</h4>
                                        <p>Implement basic tumor detection algorithms</p>
                                        <span class="difficulty hard">Hard</span>
                                    </div>
                                    <div class="exercise-status">
                                        <i class="fas fa-circle"></i>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="exercise-workspace">
                            <div id="exercise-content" class="exercise-content">
                                <div class="welcome-message">
                                    <i class="fas fa-rocket"></i>
                                    <h3>Ready to Practice?</h3>
                                    <p>Select an exercise from the left panel to get started!</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </main>

        <!-- Footer -->
        <footer class="app-footer">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>Learning Platform</h4>
                    <p>Interactive 3D Brain Tumor Segmentation</p>
                </div>
                <div class="footer-section">
                    <h4>Technologies</h4>
                    <p>Three.js • MATLAB • DICOM • WebGL</p>
                </div>
                <div class="footer-section">
                    <h4>Progress</h4>
                    <div class="progress-stats">
                        <span>Exercises Completed: <strong id="completed-exercises">0</strong></span>
                        <span>Total Score: <strong id="total-score">0</strong></span>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <!-- Modal for Exercise Details -->
    <div id="exercise-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">Exercise Title</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body" id="modal-body">
                <!-- Exercise content will be loaded here -->
            </div>
        </div>
    </div>

    <!-- JavaScript Modules -->
    <script type="module" src="js/main.js"></script>
    <script type="module" src="js/3d-viewer.js"></script>
    <script type="module" src="js/matlab-simulator.js"></script>
    <script type="module" src="js/dicom-processor.js"></script>
    <script type="module" src="js/exercises.js"></script>
    <script type="module" src="js/ui-controller.js"></script>
</body>
</html>

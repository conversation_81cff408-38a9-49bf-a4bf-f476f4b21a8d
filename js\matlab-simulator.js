// MATLAB Code Simulator and Interactive Editor

class MATLABSimulator {
    constructor() {
        this.editor = null;
        this.variables = new Map();
        this.functions = new Map();
        this.outputElement = null;
        this.isInitialized = false;
        
        this.setupBuiltinFunctions();
        this.init();
    }
    
    init() {
        this.setupCodeEditor();
        this.setupEventListeners();
        this.loadTutorialContent();
        this.isInitialized = true;
        console.log('MATLAB Simulator initialized');
    }
    
    setupCodeEditor() {
        const editorElement = document.getElementById('matlab-editor');
        const outputElement = document.getElementById('matlab-output');
        
        if (!editorElement || !outputElement) {
            console.error('Editor or output element not found');
            return;
        }
        
        this.outputElement = outputElement;
        
        // Initialize CodeMirror if available
        if (typeof CodeMirror !== 'undefined') {
            this.editor = CodeMirror.fromTextArea(editorElement, {
                mode: 'octave',
                theme: 'monokai',
                lineNumbers: true,
                autoCloseBrackets: true,
                matchBrackets: true,
                indentUnit: 4,
                tabSize: 4,
                lineWrapping: true,
                extraKeys: {
                    'Ctrl-Enter': () => this.runCode(),
                    'Cmd-Enter': () => this.runCode()
                }
            });
            
            // Set initial code
            this.editor.setValue(`% Welcome to MATLAB Interactive Learning
% Try these basic commands:

% Variables and basic operations
x = 10;
y = 5;
result = x + y;
disp(['Result: ', num2str(result)]);

% Create a simple plot
t = 0:0.1:2*pi;
y = sin(t);
plot(t, y);
title('Sine Wave');
xlabel('Time');
ylabel('Amplitude');`);
        } else {
            // Fallback for basic textarea
            editorElement.value = `% Welcome to MATLAB Interactive Learning
% Try these basic commands:

x = 10;
y = 5;
result = x + y;
disp(['Result: ', num2str(result)]);`;
        }
    }
    
    setupEventListeners() {
        // Run button
        const runButton = document.getElementById('run-matlab-code');
        if (runButton) {
            runButton.addEventListener('click', () => this.runCode());
        }
        
        // Clear button
        const clearButton = document.getElementById('clear-matlab-code');
        if (clearButton) {
            clearButton.addEventListener('click', () => this.clearCode());
        }
        
        // Clear output button
        const clearOutputButton = document.getElementById('clear-output');
        if (clearOutputButton) {
            clearOutputButton.addEventListener('click', () => this.clearOutput());
        }
    }
    
    setupBuiltinFunctions() {
        // Basic MATLAB functions simulation
        this.functions.set('disp', (args) => {
            return args.map(arg => this.formatOutput(arg)).join(' ');
        });
        
        this.functions.set('fprintf', (format, ...args) => {
            // Simple printf-style formatting
            let output = format;
            args.forEach((arg, index) => {
                output = output.replace(/%[sd]/, this.formatOutput(arg));
            });
            return output;
        });
        
        this.functions.set('plot', (x, y, options = '') => {
            this.createPlot(x, y, options);
            return 'Plot created successfully';
        });
        
        this.functions.set('sin', (x) => {
            if (Array.isArray(x)) {
                return x.map(val => Math.sin(val));
            }
            return Math.sin(x);
        });
        
        this.functions.set('cos', (x) => {
            if (Array.isArray(x)) {
                return x.map(val => Math.cos(val));
            }
            return Math.cos(x);
        });
        
        this.functions.set('sqrt', (x) => {
            if (Array.isArray(x)) {
                return x.map(val => Math.sqrt(val));
            }
            return Math.sqrt(x);
        });
        
        this.functions.set('length', (arr) => {
            return Array.isArray(arr) ? arr.length : 1;
        });
        
        this.functions.set('size', (arr) => {
            if (Array.isArray(arr)) {
                return [arr.length, 1];
            }
            return [1, 1];
        });
        
        this.functions.set('zeros', (rows, cols = 1) => {
            const result = [];
            for (let i = 0; i < rows; i++) {
                if (cols === 1) {
                    result.push(0);
                } else {
                    const row = [];
                    for (let j = 0; j < cols; j++) {
                        row.push(0);
                    }
                    result.push(row);
                }
            }
            return result;
        });
        
        this.functions.set('ones', (rows, cols = 1) => {
            const result = [];
            for (let i = 0; i < rows; i++) {
                if (cols === 1) {
                    result.push(1);
                } else {
                    const row = [];
                    for (let j = 0; j < cols; j++) {
                        row.push(1);
                    }
                    result.push(row);
                }
            }
            return result;
        });
        
        this.functions.set('rand', (rows = 1, cols = 1) => {
            const result = [];
            for (let i = 0; i < rows; i++) {
                if (cols === 1) {
                    result.push(Math.random());
                } else {
                    const row = [];
                    for (let j = 0; j < cols; j++) {
                        row.push(Math.random());
                    }
                    result.push(row);
                }
            }
            return rows === 1 && cols === 1 ? result[0] : result;
        });
        
        this.functions.set('num2str', (num) => {
            return String(num);
        });
        
        this.functions.set('title', (text) => {
            // Store for plot
            this.plotTitle = text;
            return `Title set: ${text}`;
        });
        
        this.functions.set('xlabel', (text) => {
            this.plotXLabel = text;
            return `X-label set: ${text}`;
        });
        
        this.functions.set('ylabel', (text) => {
            this.plotYLabel = text;
            return `Y-label set: ${text}`;
        });
    }
    
    runCode() {
        const code = this.editor ? this.editor.getValue() : document.getElementById('matlab-editor').value;
        
        if (!code.trim()) {
            this.addOutput('No code to execute', 'warning');
            return;
        }
        
        try {
            this.addOutput(`>> Running code...`, 'info');
            const result = this.executeCode(code);
            if (result) {
                this.addOutput(result, 'success');
            }
        } catch (error) {
            this.addOutput(`Error: ${error.message}`, 'error');
        }
    }
    
    executeCode(code) {
        const lines = code.split('\n').filter(line => {
            const trimmed = line.trim();
            return trimmed && !trimmed.startsWith('%');
        });
        
        let output = [];
        
        for (const line of lines) {
            try {
                const result = this.executeLine(line.trim());
                if (result !== null && result !== undefined) {
                    output.push(result);
                }
            } catch (error) {
                output.push(`Error in line "${line}": ${error.message}`);
            }
        }
        
        return output.join('\n');
    }
    
    executeLine(line) {
        // Handle variable assignments
        if (line.includes('=') && !line.includes('==')) {
            return this.handleAssignment(line);
        }
        
        // Handle function calls
        if (line.includes('(') && line.includes(')')) {
            return this.handleFunctionCall(line);
        }
        
        // Handle range operations (e.g., 0:0.1:2*pi)
        if (line.includes(':')) {
            return this.handleRange(line);
        }
        
        // Handle simple expressions
        return this.evaluateExpression(line);
    }
    
    handleAssignment(line) {
        const [varName, expression] = line.split('=').map(s => s.trim());
        
        if (varName.endsWith(';')) {
            // Suppress output
            const cleanVarName = varName.slice(0, -1).trim();
            const value = this.evaluateExpression(expression);
            this.variables.set(cleanVarName, value);
            return null;
        } else {
            const value = this.evaluateExpression(expression);
            this.variables.set(varName, value);
            return `${varName} = ${this.formatOutput(value)}`;
        }
    }
    
    handleFunctionCall(line) {
        // Extract function name and arguments
        const match = line.match(/(\w+)\((.*)\)/);
        if (!match) return null;
        
        const [, funcName, argsStr] = match;
        const args = this.parseArguments(argsStr);
        
        if (this.functions.has(funcName)) {
            const func = this.functions.get(funcName);
            return func(...args);
        } else {
            throw new Error(`Unknown function: ${funcName}`);
        }
    }
    
    handleRange(line) {
        // Handle MATLAB range syntax: start:step:end or start:end
        const parts = line.split(':');
        
        if (parts.length === 2) {
            // start:end (step = 1)
            const start = this.evaluateExpression(parts[0]);
            const end = this.evaluateExpression(parts[1]);
            return this.createRange(start, 1, end);
        } else if (parts.length === 3) {
            // start:step:end
            const start = this.evaluateExpression(parts[0]);
            const step = this.evaluateExpression(parts[1]);
            const end = this.evaluateExpression(parts[2]);
            return this.createRange(start, step, end);
        }
        
        return null;
    }
    
    createRange(start, step, end) {
        const result = [];
        for (let i = start; i <= end; i += step) {
            result.push(Math.round(i * 1000) / 1000); // Round to avoid floating point errors
        }
        return result;
    }
    
    evaluateExpression(expr) {
        // Replace MATLAB constants
        expr = expr.replace(/pi/g, Math.PI.toString());
        
        // Replace variables
        for (const [varName, value] of this.variables) {
            const regex = new RegExp(`\\b${varName}\\b`, 'g');
            expr = expr.replace(regex, Array.isArray(value) ? `[${value.join(',')}]` : value.toString());
        }
        
        // Handle MATLAB functions in expressions
        expr = this.replaceMATLABFunctions(expr);
        
        try {
            // Use Function constructor for safe evaluation
            return new Function('return ' + expr)();
        } catch (error) {
            throw new Error(`Invalid expression: ${expr}`);
        }
    }
    
    replaceMATLABFunctions(expr) {
        // Replace common MATLAB functions with JavaScript equivalents
        expr = expr.replace(/sin\(/g, 'Math.sin(');
        expr = expr.replace(/cos\(/g, 'Math.cos(');
        expr = expr.replace(/sqrt\(/g, 'Math.sqrt(');
        expr = expr.replace(/abs\(/g, 'Math.abs(');
        expr = expr.replace(/exp\(/g, 'Math.exp(');
        expr = expr.replace(/log\(/g, 'Math.log(');
        
        return expr;
    }
    
    parseArguments(argsStr) {
        if (!argsStr.trim()) return [];
        
        const args = [];
        const parts = argsStr.split(',');
        
        for (const part of parts) {
            const trimmed = part.trim();
            if (trimmed.startsWith("'") && trimmed.endsWith("'")) {
                // String literal
                args.push(trimmed.slice(1, -1));
            } else if (trimmed.startsWith("[") && trimmed.endsWith("]")) {
                // Array literal
                const arrayStr = trimmed.slice(1, -1);
                args.push(arrayStr.split(',').map(s => parseFloat(s.trim())));
            } else {
                // Expression
                args.push(this.evaluateExpression(trimmed));
            }
        }
        
        return args;
    }
    
    createPlot(x, y, options = '') {
        if (!Array.isArray(x) || !Array.isArray(y)) {
            throw new Error('Plot data must be arrays');
        }
        
        if (x.length !== y.length) {
            throw new Error('X and Y arrays must have the same length');
        }
        
        // Create plot using Plotly if available
        if (typeof Plotly !== 'undefined') {
            const plotData = [{
                x: x,
                y: y,
                type: 'scatter',
                mode: 'lines+markers',
                name: 'Data'
            }];
            
            const layout = {
                title: this.plotTitle || 'MATLAB Plot',
                xaxis: { title: this.plotXLabel || 'X' },
                yaxis: { title: this.plotYLabel || 'Y' },
                width: 500,
                height: 300
            };
            
            // Create plot container
            const plotContainer = document.createElement('div');
            plotContainer.style.marginTop = '10px';
            plotContainer.style.border = '1px solid #ccc';
            plotContainer.style.borderRadius = '4px';
            
            this.outputElement.appendChild(plotContainer);
            
            Plotly.newPlot(plotContainer, plotData, layout);
            
            // Reset plot labels
            this.plotTitle = null;
            this.plotXLabel = null;
            this.plotYLabel = null;
        } else {
            // Fallback: create simple ASCII plot
            return this.createASCIIPlot(x, y);
        }
    }
    
    createASCIIPlot(x, y) {
        const width = 50;
        const height = 20;
        const plot = [];
        
        // Initialize plot grid
        for (let i = 0; i < height; i++) {
            plot[i] = new Array(width).fill(' ');
        }
        
        // Find min/max values
        const minX = Math.min(...x);
        const maxX = Math.max(...x);
        const minY = Math.min(...y);
        const maxY = Math.max(...y);
        
        // Plot points
        for (let i = 0; i < x.length; i++) {
            const plotX = Math.round(((x[i] - minX) / (maxX - minX)) * (width - 1));
            const plotY = Math.round(((y[i] - minY) / (maxY - minY)) * (height - 1));
            
            if (plotX >= 0 && plotX < width && plotY >= 0 && plotY < height) {
                plot[height - 1 - plotY][plotX] = '*';
            }
        }
        
        // Convert to string
        return plot.map(row => row.join('')).join('\n');
    }
    
    formatOutput(value) {
        if (Array.isArray(value)) {
            if (value.length > 10) {
                return `[${value.slice(0, 5).join(', ')} ... ${value.slice(-2).join(', ')}] (${value.length} elements)`;
            }
            return `[${value.join(', ')}]`;
        }
        
        if (typeof value === 'number') {
            return Number.isInteger(value) ? value.toString() : value.toFixed(4);
        }
        
        return String(value);
    }
    
    addOutput(text, type = 'normal') {
        if (!this.outputElement) return;
        
        const outputLine = document.createElement('div');
        outputLine.className = `output-line ${type}`;
        
        // Handle different output types
        switch (type) {
            case 'error':
                outputLine.style.color = '#ef4444';
                break;
            case 'warning':
                outputLine.style.color = '#f97316';
                break;
            case 'success':
                outputLine.style.color = '#10b981';
                break;
            case 'info':
                outputLine.style.color = '#3b82f6';
                break;
            default:
                outputLine.style.color = '#e2e8f0';
        }
        
        outputLine.textContent = text;
        this.outputElement.appendChild(outputLine);
        
        // Auto-scroll to bottom
        this.outputElement.scrollTop = this.outputElement.scrollHeight;
    }
    
    clearCode() {
        if (this.editor) {
            this.editor.setValue('');
        } else {
            document.getElementById('matlab-editor').value = '';
        }
    }
    
    clearOutput() {
        if (this.outputElement) {
            this.outputElement.innerHTML = '';
        }
    }
    
    loadTutorialContent() {
        const tutorialContent = document.getElementById('matlab-tutorial-content');
        if (!tutorialContent) return;
        
        tutorialContent.innerHTML = `
            <div class="tutorial-section">
                <h3>MATLAB Basics Tutorial</h3>
                
                <div class="tutorial-step">
                    <h4>1. Variables and Basic Operations</h4>
                    <p>In MATLAB, you can create variables and perform basic mathematical operations:</p>
                    <div class="code-example">
                        <code>x = 10;<br>y = 5;<br>result = x + y;</code>
                    </div>
                    <p>The semicolon (;) suppresses output. Remove it to see the result.</p>
                </div>
                
                <div class="tutorial-step">
                    <h4>2. Arrays and Matrices</h4>
                    <p>MATLAB excels at working with arrays and matrices:</p>
                    <div class="code-example">
                        <code>A = [1, 2, 3; 4, 5, 6];<br>B = zeros(2, 3);<br>C = ones(3, 1);</code>
                    </div>
                </div>
                
                <div class="tutorial-step">
                    <h4>3. Functions and Plotting</h4>
                    <p>Create plots to visualize your data:</p>
                    <div class="code-example">
                        <code>t = 0:0.1:2*pi;<br>y = sin(t);<br>plot(t, y);<br>title('Sine Wave');</code>
                    </div>
                </div>
                
                <div class="tutorial-step">
                    <h4>4. Try It Yourself!</h4>
                    <p>Copy and paste the examples into the code editor and click "Run" to see the results.</p>
                </div>
            </div>
        `;
    }
}

// Make class available globally
window.MATLABSimulator = MATLABSimulator;

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline 3D Brain Image Processing with MATLAB Integration</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/plotly.js/2.26.0/plotly.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-matlab.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --matlab-gradient: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            --ai-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            --warning-gradient: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            --error-gradient: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            
            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-border: rgba(255, 255, 255, 0.2);
            --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
            
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --text-light: #94a3b8;
            
            --border-radius: 16px;
            --border-radius-lg: 24px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #ff6b35 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }
        
        /* Animated Background */
        .animated-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background: var(--primary-gradient);
        }
        
        .animated-bg::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            animation: float 20s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(1deg); }
        }
        
        /* Glass Morphism Effects */
        .glass-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius);
            box-shadow: var(--glass-shadow);
        }
        
        .glass-card-lg {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            border: 1px solid rgba(255, 255, 255, 0.25);
            border-radius: var(--border-radius-lg);
            box-shadow: 0 12px 40px 0 rgba(31, 38, 135, 0.4);
        }
        
        /* Navigation Breadcrumb */
        .nav-breadcrumb {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 15px 20px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .breadcrumb {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 0.9rem;
            color: var(--text-secondary);
        }
        
        .breadcrumb a {
            color: #2563eb;
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
            padding: 4px 8px;
            border-radius: 6px;
        }
        
        .breadcrumb a:hover {
            background: rgba(37, 99, 235, 0.1);
            color: #1d4ed8;
        }
        
        /* Header */
        .header {
            position: relative;
            padding: 60px 20px;
            text-align: center;
            overflow: hidden;
        }
        
        .header-content {
            position: relative;
            z-index: 2;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header h1 {
            font-size: clamp(2rem, 4vw, 3.5rem);
            font-weight: 800;
            margin-bottom: 15px;
            color: white;
            text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            letter-spacing: -0.02em;
        }
        
        .header .subtitle {
            font-size: clamp(1rem, 2vw, 1.2rem);
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 30px;
            font-weight: 400;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .header-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 40px;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .feature-item {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            transition: var(--transition);
        }
        
        .feature-item:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.15);
        }
        
        .feature-icon {
            font-size: 2rem;
            color: white;
            margin-bottom: 10px;
        }
        
        .feature-title {
            font-weight: 600;
            color: white;
            margin-bottom: 5px;
        }
        
        .feature-description {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.8);
        }
        
        /* Container */
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 30px 20px;
        }
        
        /* Main Layout */
        .main-layout {
            display: grid;
            grid-template-columns: 350px 1fr 350px;
            gap: 30px;
            margin-bottom: 40px;
        }
        
        /* MATLAB Integration Panel */
        .matlab-panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius-lg);
            padding: 30px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            height: fit-content;
            position: sticky;
            top: 100px;
        }
        
        .matlab-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f1f5f9;
        }
        
        .matlab-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            background: var(--matlab-gradient);
            box-shadow: 0 6px 20px rgba(255, 107, 53, 0.3);
        }
        
        .matlab-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--text-primary);
        }
        
        .matlab-status {
            background: #f8fafc;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 25px;
        }
        
        .status-indicator {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #ef4444;
            animation: pulse 2s infinite;
        }
        
        .status-dot.connected {
            background: #22c55e;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .status-text {
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .matlab-actions {
            display: grid;
            gap: 10px;
        }
        
        /* Processing Panel */
        .processing-panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius-lg);
            padding: 30px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            min-height: 600px;
        }
        
        .processing-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f1f5f9;
        }
        
        .processing-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            background: var(--ai-gradient);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
        }
        
        .processing-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
        }
        
        /* Toolbox Panel */
        .toolbox-panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius-lg);
            padding: 30px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            height: fit-content;
            position: sticky;
            top: 100px;
        }
        
        .toolbox-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .toolbox-title {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--text-primary);
        }
        
        .toolbox-grid {
            display: grid;
            gap: 15px;
        }
        
        .toolbox-item {
            background: linear-gradient(135deg, #f8fafc, #f1f5f9);
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 15px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        
        .toolbox-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--primary-gradient);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }
        
        .toolbox-item:hover::before {
            transform: scaleX(1);
        }
        
        .toolbox-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            border-color: #cbd5e1;
        }
        
        .toolbox-item.active {
            background: linear-gradient(135deg, #dbeafe, #bfdbfe);
            border-color: #3b82f6;
            transform: translateY(-3px);
        }
        
        .toolbox-item-icon {
            font-size: 1.2rem;
            color: #3b82f6;
            margin-bottom: 8px;
        }
        
        .toolbox-item-name {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 5px;
            font-size: 0.9rem;
        }
        
        .toolbox-item-description {
            font-size: 0.8rem;
            color: var(--text-secondary);
            line-height: 1.4;
        }
        
        /* Code Editor */
        .code-editor {
            background: linear-gradient(135deg, #1e293b, #334155);
            border-radius: 15px;
            margin: 25px 0;
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }
        
        .code-header {
            background: rgba(0, 0, 0, 0.2);
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .code-title {
            color: #e2e8f0;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .code-actions {
            display: flex;
            gap: 10px;
        }
        
        .code-btn {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            border: none;
            color: white;
            padding: 6px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .code-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(59, 130, 246, 0.4);
        }
        
        .code-btn.run {
            background: linear-gradient(135deg, #10b981, #059669);
        }
        
        .code-btn.run:hover {
            box-shadow: 0 5px 15px rgba(16, 185, 129, 0.4);
        }
        
        .code-content {
            padding: 20px;
            font-family: 'JetBrains Mono', 'Fira Code', 'Monaco', monospace;
            font-size: 14px;
            line-height: 1.6;
            color: #e2e8f0;
            max-height: 400px;
            overflow-y: auto;
        }
        
        /* Buttons */
        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            font-size: 0.9rem;
            text-decoration: none;
            position: relative;
            overflow: hidden;
            width: 100%;
        }
        
        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }
        
        .btn:hover::before {
            left: 100%;
        }
        
        .btn-primary {
            background: var(--primary-gradient);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }
        
        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.5);
        }
        
        .btn-matlab {
            background: var(--matlab-gradient);
            color: white;
            box-shadow: 0 4px 15px rgba(255, 107, 53, 0.4);
        }
        
        .btn-matlab:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(255, 107, 53, 0.5);
        }
        
        .btn-success {
            background: var(--success-gradient);
            color: white;
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
        }
        
        .btn-success:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.5);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        /* Info Boxes */
        .info-box {
            background: linear-gradient(135deg, #dbeafe, #bfdbfe);
            border: 1px solid #3b82f6;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            position: relative;
            overflow: hidden;
        }
        
        .info-box::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: #3b82f6;
        }
        
        .info-box.warning {
            background: linear-gradient(135deg, #fef3c7, #fde68a);
            border-color: #f59e0b;
        }
        
        .info-box.warning::before {
            background: #f59e0b;
        }
        
        .info-box.success {
            background: linear-gradient(135deg, #dcfce7, #bbf7d0);
            border-color: #22c55e;
        }
        
        .info-box.success::before {
            background: #22c55e;
        }
        
        .info-box h4 {
            color: #1e293b;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 600;
        }
        
        /* Responsive Design */
        @media (max-width: 1200px) {
            .main-layout {
                grid-template-columns: 1fr;
                gap: 25px;
            }
            
            .matlab-panel,
            .toolbox-panel {
                position: static;
            }
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 20px 15px;
            }
            
            .matlab-panel,
            .processing-panel,
            .toolbox-panel {
                padding: 20px;
            }
            
            .header-features {
                grid-template-columns: 1fr;
                gap: 15px;
            }
        }
        
        /* Terminal Output */
        .terminal-output {
            background: #1a1a1a;
            color: #00ff00;
            font-family: 'JetBrains Mono', monospace;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            max-height: 200px;
            overflow-y: auto;
            font-size: 13px;
            line-height: 1.4;
        }
        
        .terminal-prompt {
            color: #00aaff;
        }
        
        .terminal-error {
            color: #ff4444;
        }
        
        .terminal-success {
            color: #44ff44;
        }
    </style>
</head>
<body>
    <div class="animated-bg"></div>
    
    <!-- Navigation Breadcrumb -->
    <div class="nav-breadcrumb">
        <div class="breadcrumb">
            <a href="index.html"><i class="fas fa-home"></i> Home</a>
            <i class="fas fa-chevron-right"></i>
            <a href="platform.html">Platform</a>
            <i class="fas fa-chevron-right"></i>
            <span>Offline MATLAB Processing</span>
        </div>
    </div>

    <!-- Header -->
    <div class="header">
        <div class="header-content">
            <h1><i class="fas fa-desktop"></i> Offline 3D Brain Image Processing</h1>
            <p class="subtitle">Advanced MATLAB IDE integration for offline 3D brain image processing with AI classifier design and comprehensive toolbox access</p>
            
            <div class="header-features">
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-laptop-code"></i>
                    </div>
                    <div class="feature-title">MATLAB IDE</div>
                    <div class="feature-description">Direct integration with local MATLAB installation</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-brain"></i>
                    </div>
                    <div class="feature-title">AI Classifier</div>
                    <div class="feature-description">Design custom AI classifiers for brain analysis</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-cube"></i>
                    </div>
                    <div class="feature-title">3D Processing</div>
                    <div class="feature-description">Advanced 3D image processing capabilities</div>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-toolbox"></i>
                    </div>
                    <div class="feature-title">Toolbox Access</div>
                    <div class="feature-description">Full access to MATLAB specialized toolboxes</div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Main Layout -->
        <div class="main-layout">
            <!-- MATLAB Integration Panel -->
            <div class="matlab-panel">
                <div class="matlab-header">
                    <div class="matlab-icon">
                        <i class="fas fa-laptop-code"></i>
                    </div>
                    <div>
                        <div class="matlab-title">MATLAB Integration</div>
                        <div style="color: var(--text-secondary); font-size: 0.9rem;">Local IDE Connection</div>
                    </div>
                </div>

                <!-- MATLAB Status -->
                <div class="matlab-status">
                    <div class="status-indicator">
                        <div class="status-dot" id="matlab-status-dot"></div>
                        <div class="status-text" id="matlab-status-text">MATLAB Not Connected</div>
                    </div>
                    <div style="font-size: 0.9rem; color: var(--text-secondary);">
                        <div id="matlab-version">Version: Not detected</div>
                        <div id="matlab-path">Path: Not found</div>
                    </div>
                </div>

                <!-- MATLAB Actions -->
                <div class="matlab-actions">
                    <button class="btn btn-matlab" id="connect-matlab" onclick="connectMATLAB()">
                        <i class="fas fa-plug"></i> Connect to MATLAB
                    </button>
                    <button class="btn btn-primary" id="launch-matlab" onclick="launchMATLAB()" disabled>
                        <i class="fas fa-rocket"></i> Launch MATLAB IDE
                    </button>
                    <button class="btn btn-success" id="open-workspace" onclick="openWorkspace()" disabled>
                        <i class="fas fa-folder-open"></i> Open Workspace
                    </button>
                </div>

                <!-- System Requirements -->
                <div class="info-box">
                    <h4><i class="fas fa-info-circle"></i> System Requirements</h4>
                    <ul style="margin-top: 10px; line-height: 1.8; font-size: 0.9rem;">
                        <li>MATLAB R2020b or later</li>
                        <li>Image Processing Toolbox</li>
                        <li>Deep Learning Toolbox</li>
                        <li>Computer Vision Toolbox</li>
                        <li>8GB RAM minimum</li>
                        <li>GPU recommended</li>
                    </ul>
                </div>

                <!-- Quick Setup -->
                <div class="info-box warning">
                    <h4><i class="fas fa-exclamation-triangle"></i> Quick Setup</h4>
                    <ol style="margin-top: 10px; line-height: 1.8; font-size: 0.9rem;">
                        <li>Install MATLAB with required toolboxes</li>
                        <li>Add MATLAB to system PATH</li>
                        <li>Enable MATLAB Engine API</li>
                        <li>Click "Connect to MATLAB"</li>
                    </ol>
                </div>
            </div>

            <!-- Processing Panel -->
            <div class="processing-panel">
                <div class="processing-header">
                    <div class="processing-icon">
                        <i class="fas fa-brain"></i>
                    </div>
                    <div>
                        <div class="processing-title">AI Classifier Design Studio</div>
                        <div style="color: var(--text-secondary); font-size: 0.9rem;">Design and train custom AI classifiers for brain image analysis</div>
                    </div>
                </div>

                <!-- Classifier Templates -->
                <div style="margin-bottom: 25px;">
                    <h4 style="margin-bottom: 15px; color: var(--text-primary);">
                        <i class="fas fa-templates"></i> Classifier Templates
                    </h4>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                        <div class="template-card" data-template="tumor-detection">
                            <div class="template-icon">
                                <i class="fas fa-search"></i>
                            </div>
                            <div class="template-name">Tumor Detection</div>
                            <div class="template-description">CNN-based brain tumor classification</div>
                        </div>
                        <div class="template-card" data-template="tissue-segmentation">
                            <div class="template-icon">
                                <i class="fas fa-layer-group"></i>
                            </div>
                            <div class="template-name">Tissue Segmentation</div>
                            <div class="template-description">Multi-class tissue classification</div>
                        </div>
                        <div class="template-card" data-template="anomaly-detection">
                            <div class="template-icon">
                                <i class="fas fa-exclamation-circle"></i>
                            </div>
                            <div class="template-name">Anomaly Detection</div>
                            <div class="template-description">Unsupervised anomaly identification</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Toolbox Panel -->
            <div class="toolbox-panel">
                <div class="toolbox-header">
                    <i class="fas fa-toolbox"></i>
                    <div class="toolbox-title">MATLAB Toolboxes</div>
                </div>

                <div class="toolbox-grid">
                    <div class="toolbox-item active" data-toolbox="image-processing">
                        <div class="toolbox-item-icon">
                            <i class="fas fa-image"></i>
                        </div>
                        <div class="toolbox-item-name">Image Processing</div>
                        <div class="toolbox-item-description">Advanced image analysis and enhancement</div>
                    </div>

                    <div class="toolbox-item" data-toolbox="deep-learning">
                        <div class="toolbox-item-icon">
                            <i class="fas fa-brain"></i>
                        </div>
                        <div class="toolbox-item-name">Deep Learning</div>
                        <div class="toolbox-item-description">Neural networks and AI model design</div>
                    </div>

                    <div class="toolbox-item" data-toolbox="computer-vision">
                        <div class="toolbox-item-icon">
                            <i class="fas fa-eye"></i>
                        </div>
                        <div class="toolbox-item-name">Computer Vision</div>
                        <div class="toolbox-item-description">Object detection and recognition</div>
                    </div>

                    <div class="toolbox-item" data-toolbox="statistics">
                        <div class="toolbox-item-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <div class="toolbox-item-name">Statistics & ML</div>
                        <div class="toolbox-item-description">Statistical analysis and machine learning</div>
                    </div>

                    <div class="toolbox-item" data-toolbox="signal-processing">
                        <div class="toolbox-item-icon">
                            <i class="fas fa-wave-square"></i>
                        </div>
                        <div class="toolbox-item-name">Signal Processing</div>
                        <div class="toolbox-item-description">Digital signal analysis and filtering</div>
                    </div>

                    <div class="toolbox-item" data-toolbox="parallel-computing">
                        <div class="toolbox-item-icon">
                            <i class="fas fa-microchip"></i>
                        </div>
                        <div class="toolbox-item-name">Parallel Computing</div>
                        <div class="toolbox-item-description">GPU and cluster computing</div>
                    </div>
                </div>

                <!-- Toolbox Functions -->
                <div style="margin-top: 25px;">
                    <h5 style="margin-bottom: 15px; color: var(--text-primary);">
                        <i class="fas fa-functions"></i> Available Functions
                    </h5>
                    <div id="toolbox-functions" style="background: #f8fafc; border-radius: 8px; padding: 15px; font-family: monospace; font-size: 0.8rem; max-height: 200px; overflow-y: auto;">
                        <div style="color: var(--text-secondary);">Select a toolbox to view available functions...</div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div style="margin-top: 25px;">
                    <h5 style="margin-bottom: 15px; color: var(--text-primary);">
                        <i class="fas fa-bolt"></i> Quick Actions
                    </h5>
                    <div style="display: grid; gap: 8px;">
                        <button class="btn btn-primary" onclick="loadSampleData()" style="font-size: 0.8rem; padding: 8px 12px;">
                            <i class="fas fa-database"></i> Load Sample Data
                        </button>
                        <button class="btn btn-success" onclick="openDocumentation()" style="font-size: 0.8rem; padding: 8px 12px;">
                            <i class="fas fa-book"></i> Open Documentation
                        </button>
                        <button class="btn btn-matlab" onclick="checkLicense()" style="font-size: 0.8rem; padding: 8px 12px;">
                            <i class="fas fa-key"></i> Check License
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Code Editor Section -->
        <div style="margin-top: 30px;">
            <div class="code-editor">
                <div class="code-header">
                    <div class="code-title">
                        <i class="fas fa-code"></i>
                        <span id="current-script">brain_tumor_classifier.m</span>
                    </div>
                    <div class="code-actions">
                        <button class="code-btn" onclick="saveScript()">
                            <i class="fas fa-save"></i> Save
                        </button>
                        <button class="code-btn run" onclick="runScript()">
                            <i class="fas fa-play"></i> Run
                        </button>
                        <button class="code-btn" onclick="exportScript()">
                            <i class="fas fa-download"></i> Export
                        </button>
                    </div>
                </div>
                <div class="code-content" id="code-editor">
<pre><code class="language-matlab">function [trainedClassifier, validationAccuracy] = trainBrainTumorClassifier(trainingData)
    % Advanced Brain Tumor Classifier using Deep Learning
    %
    % Features:
    % - 3D CNN architecture optimized for brain MRI
    % - Data augmentation for improved generalization
    % - Transfer learning from pre-trained networks
    % - Advanced preprocessing pipeline
    % - Real-time validation and monitoring

    fprintf('🧠 Initializing Brain Tumor Classifier Training...\n');

    %% Step 1: Data Preprocessing and Augmentation
    fprintf('📊 Preprocessing training data...\n');

    % Define image input layer for 3D MRI data
    inputSize = [128 128 64 1]; % [H W D C]
    numClasses = 4; % Background, Healthy, Tumor, Edema

    % Create image datastore with augmentation
    imageAugmenter = imageDataAugmenter( ...
        'RandRotation', [-15 15], ...
        'RandXTranslation', [-10 10], ...
        'RandYTranslation', [-10 10], ...
        'RandXReflection', true, ...
        'RandYReflection', true);

    %% Step 2: Advanced 3D CNN Architecture
    fprintf('🏗️ Building 3D CNN architecture...\n');

    layers = [
        % Input layer
        image3dInputLayer(inputSize, 'Name', 'input', 'Normalization', 'zscore')

        % Encoder Block 1
        convolution3dLayer(3, 32, 'Padding', 'same', 'Name', 'conv1_1')
        batchNormalizationLayer('Name', 'bn1_1')
        reluLayer('Name', 'relu1_1')
        convolution3dLayer(3, 32, 'Padding', 'same', 'Name', 'conv1_2')
        batchNormalizationLayer('Name', 'bn1_2')
        reluLayer('Name', 'relu1_2')
        maxPooling3dLayer(2, 'Stride', 2, 'Name', 'pool1')

        % Encoder Block 2
        convolution3dLayer(3, 64, 'Padding', 'same', 'Name', 'conv2_1')
        batchNormalizationLayer('Name', 'bn2_1')
        reluLayer('Name', 'relu2_1')
        convolution3dLayer(3, 64, 'Padding', 'same', 'Name', 'conv2_2')
        batchNormalizationLayer('Name', 'bn2_2')
        reluLayer('Name', 'relu2_2')
        maxPooling3dLayer(2, 'Stride', 2, 'Name', 'pool2')

        % Bottleneck with Attention
        convolution3dLayer(3, 256, 'Padding', 'same', 'Name', 'bottleneck1')
        batchNormalizationLayer('Name', 'bn_bottleneck1')
        reluLayer('Name', 'relu_bottleneck1')
        dropoutLayer(0.5, 'Name', 'dropout_bottleneck')

        % Global Average Pooling
        globalAveragePooling3dLayer('Name', 'gap')

        % Classification Head
        fullyConnectedLayer(512, 'Name', 'fc1')
        reluLayer('Name', 'relu_fc1')
        dropoutLayer(0.5, 'Name', 'dropout_fc1')
        fullyConnectedLayer(numClasses, 'Name', 'fc_final')
        softmaxLayer('Name', 'softmax')
        classificationLayer('Name', 'output')
    ];

    %% Step 3: Training Configuration
    fprintf('⚙️ Configuring training parameters...\n');

    options = trainingOptions('adam', ...
        'InitialLearnRate', 1e-4, ...
        'MaxEpochs', 50, ...
        'MiniBatchSize', 4, ...
        'ValidationFrequency', 10, ...
        'ValidationPatience', 5, ...
        'Shuffle', 'every-epoch', ...
        'Verbose', true, ...
        'Plots', 'training-progress', ...
        'ExecutionEnvironment', 'auto');

    %% Step 4: Model Training
    fprintf('🚀 Starting model training...\n');

    % Train the network
    [trainedClassifier, trainInfo] = trainNetwork(trainingData, layers, options);

    %% Step 5: Validation and Performance Analysis
    fprintf('📈 Evaluating model performance...\n');

    % Calculate validation accuracy
    validationAccuracy = max(trainInfo.ValidationAccuracy);

    % Display training summary
    fprintf('✅ Training completed successfully!\n');
    fprintf('📊 Final Validation Accuracy: %.2f%%\n', validationAccuracy);

    % Save the trained model
    save('trainedBrainTumorClassifier.mat', 'trainedClassifier', 'trainInfo');
    fprintf('💾 Model saved as trainedBrainTumorClassifier.mat\n');

end</code></pre>
                </div>
            </div>

            <!-- Terminal Output -->
            <div class="terminal-output" id="terminal-output">
                <div class="terminal-prompt">MATLAB R2023b (Update 5) Command Window</div>
                <div>Ready for commands...</div>
                <div class="terminal-prompt">>> </div>
            </div>

            <!-- Processing Status -->
            <div id="processing-status" style="display: none;">
                <div class="info-box">
                    <h4><i class="fas fa-cog fa-spin"></i> Processing Status</h4>
                    <div id="status-message">Initializing classifier training...</div>
                    <div style="margin-top: 10px;">
                        <div style="background: #e2e8f0; border-radius: 10px; height: 8px; overflow: hidden;">
                            <div id="progress-bar" style="background: var(--primary-gradient); height: 100%; width: 0%; transition: width 0.3s ease;"></div>
                        </div>
                        <div style="margin-top: 5px; font-size: 0.9rem; color: var(--text-secondary);">
                            <span id="progress-text">0% complete</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Offline MATLAB Processing Application
        class OfflineMATLABProcessor {
            constructor() {
                this.matlabConnected = false;
                this.currentToolbox = 'image-processing';
                this.activeTemplate = null;
                this.processingActive = false;
                this.init();
            }

            init() {
                console.log('🚀 Initializing Offline MATLAB Processing Platform...');
                this.setupToolboxSelection();
                this.setupTemplateCards();
                this.setupAnimations();
                this.checkMATLABStatus();
                console.log('✅ Platform ready!');
            }

            setupToolboxSelection() {
                const toolboxItems = document.querySelectorAll('.toolbox-item');
                toolboxItems.forEach(item => {
                    item.addEventListener('click', () => {
                        // Remove active class from all items
                        toolboxItems.forEach(i => i.classList.remove('active'));
                        // Add active class to clicked item
                        item.classList.add('active');

                        const toolbox = item.dataset.toolbox;
                        this.currentToolbox = toolbox;
                        this.updateToolboxFunctions(toolbox);
                        this.showNotification(`Switched to ${item.querySelector('.toolbox-item-name').textContent}`, 'info');
                    });
                });
            }

            setupTemplateCards() {
                // Add CSS for template cards
                const style = document.createElement('style');
                style.textContent = `
                    .template-card {
                        background: linear-gradient(135deg, #f8fafc, #f1f5f9);
                        border: 1px solid #e2e8f0;
                        border-radius: 12px;
                        padding: 20px;
                        text-align: center;
                        cursor: pointer;
                        transition: var(--transition);
                        position: relative;
                        overflow: hidden;
                    }

                    .template-card::before {
                        content: '';
                        position: absolute;
                        top: 0;
                        left: 0;
                        right: 0;
                        height: 3px;
                        background: var(--primary-gradient);
                        transform: scaleX(0);
                        transition: transform 0.3s ease;
                    }

                    .template-card:hover::before {
                        transform: scaleX(1);
                    }

                    .template-card:hover {
                        transform: translateY(-5px);
                        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
                        border-color: #cbd5e1;
                    }

                    .template-card.active {
                        background: linear-gradient(135deg, #dbeafe, #bfdbfe);
                        border-color: #3b82f6;
                        transform: translateY(-5px);
                    }

                    .template-icon {
                        font-size: 2rem;
                        color: #3b82f6;
                        margin-bottom: 15px;
                    }

                    .template-name {
                        font-weight: 600;
                        color: var(--text-primary);
                        margin-bottom: 8px;
                        font-size: 1.1rem;
                    }

                    .template-description {
                        color: var(--text-secondary);
                        font-size: 0.9rem;
                        line-height: 1.4;
                    }
                `;
                document.head.appendChild(style);

                // Add event listeners
                const templateCards = document.querySelectorAll('.template-card');
                templateCards.forEach(card => {
                    card.addEventListener('click', () => {
                        templateCards.forEach(c => c.classList.remove('active'));
                        card.classList.add('active');

                        const template = card.dataset.template;
                        this.activeTemplate = template;
                        this.loadTemplate(template);
                    });
                });
            }

            setupAnimations() {
                if (!window.gsap) return;

                // Animate panels on load
                gsap.fromTo('.matlab-panel',
                    { opacity: 0, x: -50 },
                    { duration: 0.8, opacity: 1, x: 0, ease: "power2.out" }
                );

                gsap.fromTo('.processing-panel',
                    { opacity: 0, y: 30 },
                    { duration: 0.8, opacity: 1, y: 0, ease: "power2.out", delay: 0.2 }
                );

                gsap.fromTo('.toolbox-panel',
                    { opacity: 0, x: 50 },
                    { duration: 0.8, opacity: 1, x: 0, ease: "power2.out", delay: 0.4 }
                );

                // Animate feature items
                const featureItems = document.querySelectorAll('.feature-item');
                gsap.fromTo(featureItems,
                    { opacity: 0, y: 20 },
                    {
                        duration: 0.6,
                        opacity: 1,
                        y: 0,
                        stagger: 0.1,
                        ease: "back.out(1.7)"
                    }
                );
            }

            checkMATLABStatus() {
                // Simulate MATLAB detection
                setTimeout(() => {
                    const isInstalled = Math.random() > 0.3; // 70% chance of detection

                    if (isInstalled) {
                        this.simulateMATLABDetection();
                    } else {
                        this.showMATLABNotFound();
                    }
                }, 1000);
            }

            simulateMATLABDetection() {
                const statusDot = document.getElementById('matlab-status-dot');
                const statusText = document.getElementById('matlab-status-text');
                const versionText = document.getElementById('matlab-version');
                const pathText = document.getElementById('matlab-path');

                statusDot.classList.add('connected');
                statusText.textContent = 'MATLAB Detected';
                versionText.textContent = 'Version: R2023b (Update 5)';
                pathText.textContent = 'Path: C:\\Program Files\\MATLAB\\R2023b';

                // Enable buttons
                document.getElementById('connect-matlab').disabled = false;

                this.showNotification('✅ MATLAB installation detected!', 'success');
            }

            showMATLABNotFound() {
                this.showNotification('❌ MATLAB not found. Please install MATLAB and required toolboxes.', 'warning');
            }

            updateToolboxFunctions(toolbox) {
                const functionsContainer = document.getElementById('toolbox-functions');

                const toolboxFunctions = {
                    'image-processing': [
                        'imread', 'imwrite', 'imshow', 'imresize', 'imfilter',
                        'edge', 'bwlabel', 'regionprops', 'imopen', 'imclose',
                        'imdilate', 'imerode', 'imtophat', 'imbothat', 'watershed'
                    ],
                    'deep-learning': [
                        'trainNetwork', 'classify', 'activations', 'layerGraph',
                        'convolution2dLayer', 'fullyConnectedLayer', 'reluLayer',
                        'maxPooling2dLayer', 'batchNormalizationLayer', 'dropoutLayer'
                    ],
                    'computer-vision': [
                        'detectSURFFeatures', 'extractFeatures', 'matchFeatures',
                        'estimateGeometricTransform', 'vision.CascadeObjectDetector',
                        'insertObjectAnnotation', 'step', 'release'
                    ],
                    'statistics': [
                        'fitcsvm', 'fitctree', 'fitcensemble', 'crossval',
                        'kfoldLoss', 'predict', 'kmeans', 'pca', 'tsne'
                    ],
                    'signal-processing': [
                        'fft', 'ifft', 'filter', 'butter', 'cheby1', 'cheby2',
                        'ellip', 'freqz', 'pwelch', 'spectrogram'
                    ],
                    'parallel-computing': [
                        'parfor', 'spmd', 'parpool', 'parfeval', 'gpuArray',
                        'gather', 'arrayfun', 'bsxfun', 'pagefun'
                    ]
                };

                const functions = toolboxFunctions[toolbox] || [];
                functionsContainer.innerHTML = functions.map(func =>
                    `<span style="color: #3b82f6; margin-right: 10px; cursor: pointer;" onclick="insertFunction('${func}')">${func}</span>`
                ).join('');
            }

            loadTemplate(template) {
                const templates = {
                    'tumor-detection': `function [trainedClassifier, accuracy] = trainTumorDetector(dataPath)
    % Brain Tumor Detection using CNN

    % Load and preprocess data
    imds = imageDatastore(dataPath, 'IncludeSubfolders', true, 'LabelSource', 'foldernames');

    % Split data
    [imdsTrain, imdsValidation] = splitEachLabel(imds, 0.8, 'randomized');

    % Define network architecture
    layers = [
        imageInputLayer([224 224 3])
        convolution2dLayer(3, 32, 'Padding', 'same')
        batchNormalizationLayer
        reluLayer
        maxPooling2dLayer(2, 'Stride', 2)

        convolution2dLayer(3, 64, 'Padding', 'same')
        batchNormalizationLayer
        reluLayer
        maxPooling2dLayer(2, 'Stride', 2)

        fullyConnectedLayer(128)
        reluLayer
        dropoutLayer(0.5)
        fullyConnectedLayer(2)
        softmaxLayer
        classificationLayer
    ];

    % Training options
    options = trainingOptions('adam', ...
        'InitialLearnRate', 1e-4, ...
        'MaxEpochs', 20, ...
        'MiniBatchSize', 16, ...
        'ValidationData', imdsValidation, ...
        'ValidationFrequency', 10, ...
        'Plots', 'training-progress');

    % Train network
    trainedClassifier = trainNetwork(imdsTrain, layers, options);

    % Evaluate accuracy
    YPred = classify(trainedClassifier, imdsValidation);
    YValidation = imdsValidation.Labels;
    accuracy = sum(YPred == YValidation) / numel(YValidation);

    fprintf('Validation Accuracy: %.2f%%\\n', accuracy * 100);
end`,

                    'tissue-segmentation': `function segmentedImage = segmentBrainTissue(mriImage)
    % Multi-class Brain Tissue Segmentation

    % Preprocessing
    mriImage = mat2gray(mriImage);
    mriImage = imresize(mriImage, [256 256]);

    % Feature extraction
    features = extractImageFeatures(mriImage);

    % K-means clustering for tissue segmentation
    numClusters = 4; % CSF, Gray Matter, White Matter, Background
    [clusterIdx, centroids] = kmeans(features, numClusters);

    % Reshape to image dimensions
    segmentedImage = reshape(clusterIdx, size(mriImage));

    % Post-processing
    segmentedImage = medfilt2(segmentedImage, [3 3]);

    % Visualize results
    figure;
    subplot(1,2,1); imshow(mriImage); title('Original MRI');
    subplot(1,2,2); imshow(label2rgb(segmentedImage)); title('Segmented Tissues');
end

function features = extractImageFeatures(image)
    % Extract texture and intensity features

    % Intensity features
    intensityFeatures = image(:);

    % Texture features using GLCM
    glcm = graycomatrix(image);
    stats = graycoprops(glcm, {'Contrast', 'Correlation', 'Energy', 'Homogeneity'});

    % Replicate texture features for each pixel
    textureFeatures = repmat([stats.Contrast, stats.Correlation, stats.Energy, stats.Homogeneity], ...
                            numel(image), 1);

    % Combine features
    features = [intensityFeatures, textureFeatures];
end`,

                    'anomaly-detection': `function anomalyMap = detectBrainAnomalies(mriVolume)
    % Unsupervised Brain Anomaly Detection

    % Preprocessing
    mriVolume = mat2gray(mriVolume);

    % Extract patches for analysis
    patchSize = [16 16 16];
    patches = extractPatches(mriVolume, patchSize);

    % Feature extraction using PCA
    [coeff, score, ~] = pca(patches);

    % Keep top components (95% variance)
    cumVar = cumsum(explained) / sum(explained);
    numComponents = find(cumVar >= 0.95, 1);
    reducedFeatures = score(:, 1:numComponents);

    % Anomaly detection using isolation forest
    anomalyScores = isolationForest(reducedFeatures);

    % Reconstruct anomaly map
    anomalyMap = reconstructFromPatches(anomalyScores, size(mriVolume), patchSize);

    % Threshold for anomaly detection
    threshold = prctile(anomalyScores, 95);
    anomalyMask = anomalyMap > threshold;

    % Visualize results
    figure;
    montage(anomalyMask, 'Size', [4 4]);
    title('Detected Anomalies');
end

function patches = extractPatches(volume, patchSize)
    % Extract overlapping patches from 3D volume
    [h, w, d] = size(volume);
    patches = [];

    for i = 1:patchSize(1):h-patchSize(1)+1
        for j = 1:patchSize(2):w-patchSize(2)+1
            for k = 1:patchSize(3):d-patchSize(3)+1
                patch = volume(i:i+patchSize(1)-1, j:j+patchSize(2)-1, k:k+patchSize(3)-1);
                patches = [patches; patch(:)'];
            end
        end
    end
end`
                };

                const codeEditor = document.getElementById('code-editor');
                const scriptName = document.getElementById('current-script');

                if (templates[template]) {
                    codeEditor.innerHTML = `<pre><code class="language-matlab">${templates[template]}</code></pre>`;
                    scriptName.textContent = `${template.replace('-', '_')}.m`;

                    // Re-highlight syntax
                    if (window.Prism) {
                        Prism.highlightAll();
                    }

                    this.showNotification(`✅ Loaded ${template} template`, 'success');
                }
            }

            showNotification(message, type = 'info') {
                const notification = document.createElement('div');
                notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    padding: 12px 20px;
                    border-radius: 8px;
                    color: white;
                    z-index: 1000;
                    max-width: 350px;
                    font-weight: 500;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                `;

                const colors = {
                    'success': '#10b981',
                    'warning': '#f59e0b',
                    'error': '#ef4444',
                    'info': '#3b82f6'
                };

                notification.style.backgroundColor = colors[type] || colors.info;
                notification.textContent = message;
                document.body.appendChild(notification);

                if (window.gsap) {
                    gsap.fromTo(notification,
                        { x: 100, opacity: 0 },
                        { duration: 0.5, x: 0, opacity: 1, ease: "back.out(1.7)" }
                    );

                    setTimeout(() => {
                        gsap.to(notification, {
                            duration: 0.3,
                            x: 100,
                            opacity: 0,
                            onComplete: () => notification.remove()
                        });
                    }, 4000);
                } else {
                    setTimeout(() => notification.remove(), 4000);
                }
            }
        }

        // Global functions
        function connectMATLAB() {
            const app = window.matlabApp;
            const connectBtn = document.getElementById('connect-matlab');
            const launchBtn = document.getElementById('launch-matlab');
            const workspaceBtn = document.getElementById('open-workspace');

            connectBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Connecting...';
            connectBtn.disabled = true;

            // Simulate connection process
            setTimeout(() => {
                app.matlabConnected = true;

                // Update UI
                const statusDot = document.getElementById('matlab-status-dot');
                const statusText = document.getElementById('matlab-status-text');

                statusDot.classList.add('connected');
                statusText.textContent = 'MATLAB Connected';

                connectBtn.innerHTML = '<i class="fas fa-check"></i> Connected';
                connectBtn.style.background = 'var(--success-gradient)';

                // Enable other buttons
                launchBtn.disabled = false;
                workspaceBtn.disabled = false;

                app.showNotification('🚀 Successfully connected to MATLAB!', 'success');

                // Add terminal output
                const terminal = document.getElementById('terminal-output');
                terminal.innerHTML += `
                    <div class="terminal-success">✅ MATLAB Engine started successfully</div>
                    <div class="terminal-success">📦 Toolboxes loaded: Image Processing, Deep Learning, Computer Vision</div>
                    <div class="terminal-prompt">>> </div>
                `;
            }, 2000);
        }

        function launchMATLAB() {
            window.matlabApp.showNotification('🚀 Launching MATLAB IDE...', 'info');

            // Simulate MATLAB launch
            setTimeout(() => {
                // In a real implementation, this would use:
                // - MATLAB Engine API for Python/JavaScript
                // - System calls to launch MATLAB
                // - WebSocket connection for communication

                window.matlabApp.showNotification('✅ MATLAB IDE launched successfully!', 'success');

                const terminal = document.getElementById('terminal-output');
                terminal.innerHTML += `
                    <div class="terminal-success">🖥️ MATLAB IDE launched in separate window</div>
                    <div class="terminal-success">🔗 Workspace synchronized</div>
                    <div class="terminal-prompt">>> </div>
                `;
            }, 1500);
        }

        function openWorkspace() {
            window.matlabApp.showNotification('📁 Opening MATLAB workspace...', 'info');

            setTimeout(() => {
                window.matlabApp.showNotification('✅ Workspace opened successfully!', 'success');

                const terminal = document.getElementById('terminal-output');
                terminal.innerHTML += `
                    <div class="terminal-success">📂 Workspace: C:\\Users\\<USER>\\MATLAB\\BrainImaging</div>
                    <div class="terminal-success">📄 Files loaded: 15 .m files, 8 .mat files</div>
                    <div class="terminal-prompt">>> </div>
                `;
            }, 1000);
        }

        function runScript() {
            const app = window.matlabApp;

            if (!app.matlabConnected) {
                app.showNotification('❌ Please connect to MATLAB first', 'error');
                return;
            }

            app.processingActive = true;

            // Show processing status
            const statusDiv = document.getElementById('processing-status');
            statusDiv.style.display = 'block';

            // Simulate script execution
            const steps = [
                { message: 'Initializing classifier training...', progress: 10 },
                { message: 'Loading training data...', progress: 25 },
                { message: 'Building CNN architecture...', progress: 40 },
                { message: 'Training neural network...', progress: 70 },
                { message: 'Evaluating performance...', progress: 90 },
                { message: 'Training completed successfully!', progress: 100 }
            ];

            let currentStep = 0;
            const interval = setInterval(() => {
                if (currentStep >= steps.length) {
                    clearInterval(interval);
                    app.processingActive = false;
                    statusDiv.style.display = 'none';

                    const terminal = document.getElementById('terminal-output');
                    terminal.innerHTML += `
                        <div class="terminal-success">🧠 Brain tumor classifier training completed</div>
                        <div class="terminal-success">📊 Validation Accuracy: 94.2%</div>
                        <div class="terminal-success">💾 Model saved: trainedBrainTumorClassifier.mat</div>
                        <div class="terminal-prompt">>> </div>
                    `;

                    app.showNotification('✅ Script executed successfully!', 'success');
                    return;
                }

                const step = steps[currentStep];
                document.getElementById('status-message').textContent = step.message;
                document.getElementById('progress-bar').style.width = step.progress + '%';
                document.getElementById('progress-text').textContent = step.progress + '% complete';

                currentStep++;
            }, 1500);
        }

        function saveScript() {
            const scriptName = document.getElementById('current-script').textContent;
            window.matlabApp.showNotification(`💾 Script saved: ${scriptName}`, 'success');
        }

        function exportScript() {
            const scriptName = document.getElementById('current-script').textContent;
            const codeContent = document.getElementById('code-editor').textContent;

            const blob = new Blob([codeContent], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = scriptName;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            window.matlabApp.showNotification(`📥 Script exported: ${scriptName}`, 'success');
        }

        function insertFunction(funcName) {
            window.matlabApp.showNotification(`📋 Function "${funcName}" copied to clipboard`, 'info');
            navigator.clipboard.writeText(funcName);
        }

        function loadSampleData() {
            window.matlabApp.showNotification('📊 Loading sample brain MRI dataset...', 'info');

            setTimeout(() => {
                const terminal = document.getElementById('terminal-output');
                terminal.innerHTML += `
                    <div class="terminal-success">📂 Sample data loaded: BraTS2023 dataset</div>
                    <div class="terminal-success">🧠 155 brain MRI volumes with annotations</div>
                    <div class="terminal-success">📊 Data split: 80% train, 20% validation</div>
                    <div class="terminal-prompt">>> </div>
                `;

                window.matlabApp.showNotification('✅ Sample data loaded successfully!', 'success');
            }, 2000);
        }

        function openDocumentation() {
            window.matlabApp.showNotification('📚 Opening MATLAB documentation...', 'info');
            // In real implementation, would open MATLAB help browser
        }

        function checkLicense() {
            window.matlabApp.showNotification('🔑 Checking MATLAB license...', 'info');

            setTimeout(() => {
                const terminal = document.getElementById('terminal-output');
                terminal.innerHTML += `
                    <div class="terminal-success">✅ MATLAB license valid</div>
                    <div class="terminal-success">📦 Available toolboxes: 12</div>
                    <div class="terminal-success">⏰ License expires: 2024-12-31</div>
                    <div class="terminal-prompt">>> </div>
                `;

                window.matlabApp.showNotification('✅ License check completed!', 'success');
            }, 1500);
        }

        // Initialize application
        document.addEventListener('DOMContentLoaded', () => {
            window.matlabApp = new OfflineMATLABProcessor();
            console.log('🧠💻 Offline MATLAB Processing Platform loaded successfully!');
        });
    </script>
</body>
</html>

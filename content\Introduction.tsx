
import React from 'react';
import SectionCard from '../components/SectionCard';

const Introduction: React.FC = () => {
  return (
    <div>
      <h2 className="text-3xl font-extrabold text-slate-900 dark:text-white mb-6">مرحبًا بك في مركز تعلم MATLAB و DICOM</h2>
      <p className="text-lg text-slate-600 dark:text-slate-300 mb-8">
        تم تصميم هذا الموقع ليكون دليلك الشامل لتعلم أساسيات برمجة الماتلاب وتطبيقاتها المتقدمة في معالجة الصور الطبية بصيغة DICOM.
      </p>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white dark:bg-slate-800 rounded-xl shadow-lg p-6">
          <h3 className="font-bold text-xl mb-2 text-sky-600 dark:text-sky-400">ماذا ستتعلم؟</h3>
          <ul className="list-disc list-inside space-y-2 text-slate-600 dark:text-slate-300">
            <li>أساسيات الماتلاب من متغيرات ومصفوفات ورسوم بيانية.</li>
            <li>التعامل مع صور DICOM الطبية، من القراءة إلى التحليل.</li>
            <li>تطبيقات عملية واقعية في مجال تشخيص الأمراض.</li>
            <li>كتابة أكواد نظيفة وفعالة لمشاريعك الخاصة.</li>
          </ul>
        </div>
        <div className="bg-white dark:bg-slate-800 rounded-xl shadow-lg p-6">
          <h3 className="font-bold text-xl mb-2 text-sky-600 dark:text-sky-400">مميزات الموقع</h3>
          <ul className="list-disc list-inside space-y-2 text-slate-600 dark:text-slate-300">
            <li>واجهة عربية كاملة مع دعم RTL.</li>
            <li>أكواد تفاعلية قابلة للنسخ بسهولة.</li>
            <li>تصميم عصري ومتجاوب مع جميع الشاشات.</li>
            <li>أمثلة واقعية من المجال الطبي.</li>
          </ul>
        </div>
      </div>
      
       <div className="mt-10 text-center">
         <p className="text-slate-500 dark:text-slate-400">استخدم القائمة الجانبية لبدء رحلتك التعليمية!</p>
       </div>
    </div>
  );
};

export default Introduction;

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick Test - Brain Segmentation Platform</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-card {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 10px;
            background: #2563eb;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 500;
            transition: background 0.2s;
        }
        .btn:hover {
            background: #1d4ed8;
        }
        .btn.success {
            background: #10b981;
        }
        .btn.warning {
            background: #f59e0b;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 6px;
            font-weight: 500;
        }
        .status.good {
            background: #dcfce7;
            color: #166534;
            border: 1px solid #bbf7d0;
        }
        .status.warning {
            background: #fef3c7;
            color: #92400e;
            border: 1px solid #fde68a;
        }
        .icon {
            font-size: 3rem;
            margin-bottom: 20px;
        }
    </style>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="test-card">
        <div class="icon">🧠</div>
        <h1>3D Brain Tumor Segmentation Platform</h1>
        <p>Quick functionality test and access portal</p>
        
        <div id="status" class="status good">
            <i class="fas fa-check-circle"></i>
            Platform files are ready to use!
        </div>
        
        <div>
            <a href="brain-segmentation.html" class="btn success">
                <i class="fas fa-rocket"></i> Launch Platform
            </a>
            <a href="demo.html" class="btn">
                <i class="fas fa-eye"></i> View Demo
            </a>
            <a href="test.html" class="btn warning">
                <i class="fas fa-cog"></i> Run Tests
            </a>
        </div>
        
        <div style="margin-top: 30px; padding: 20px; background: #f8fafc; border-radius: 8px; text-align: left;">
            <h3>🚀 Quick Start:</h3>
            <ol style="color: #64748b; line-height: 1.6;">
                <li><strong>Launch Platform:</strong> Click the green button above</li>
                <li><strong>Navigate:</strong> Use the top navigation to explore modules</li>
                <li><strong>MATLAB Basics:</strong> Try the interactive code editor</li>
                <li><strong>3D Visualization:</strong> Initialize the 3D brain viewer</li>
                <li><strong>Exercises:</strong> Practice with hands-on coding challenges</li>
            </ol>
        </div>
        
        <div style="margin-top: 20px; padding: 15px; background: #e0f2fe; border-radius: 8px;">
            <h4 style="color: #0369a1; margin-bottom: 10px;">✨ Features Available:</h4>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; font-size: 0.9rem; color: #0369a1;">
                <div>✅ Interactive MATLAB Editor</div>
                <div>✅ 3D Brain Visualization</div>
                <div>✅ DICOM Image Processing</div>
                <div>✅ Progressive Exercises</div>
                <div>✅ Real-time Code Execution</div>
                <div>✅ Responsive Design</div>
                <div>✅ Dark/Light Themes</div>
                <div>✅ Accessibility Support</div>
            </div>
        </div>
        
        <div style="margin-top: 20px; font-size: 0.9rem; color: #64748b;">
            <p><strong>System Requirements:</strong> Modern browser with JavaScript enabled</p>
            <p><strong>Recommended:</strong> Chrome, Firefox, Safari, or Edge (latest versions)</p>
        </div>
    </div>

    <script>
        // Simple functionality check
        function checkPlatform() {
            const status = document.getElementById('status');
            
            // Check if we can access the main HTML file
            fetch('brain-segmentation.html', { method: 'HEAD' })
                .then(response => {
                    if (response.ok) {
                        status.className = 'status good';
                        status.innerHTML = '<i class="fas fa-check-circle"></i> Platform is ready to launch!';
                    } else {
                        status.className = 'status warning';
                        status.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Platform files may not be accessible. Try using a local server.';
                    }
                })
                .catch(error => {
                    status.className = 'status warning';
                    status.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Please run from a web server (not file:// protocol)';
                });
        }
        
        // Run check when page loads
        window.addEventListener('load', checkPlatform);
        
        // Add some interactivity
        document.addEventListener('click', (e) => {
            if (e.target.matches('.btn')) {
                e.target.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    e.target.style.transform = 'scale(1)';
                }, 150);
            }
        });
    </script>
</body>
</html>

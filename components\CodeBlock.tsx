
import React, { useState } from 'react';
import ClipboardIcon from './icons/ClipboardIcon';
import CheckIcon from './icons/CheckIcon';

interface CodeBlockProps {
  code: string;
}

const CodeBlock: React.FC<CodeBlockProps> = ({ code }) => {
  const [copied, setCopied] = useState(false);

  const handleCopy = () => {
    navigator.clipboard.writeText(code).then(() => {
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    });
  };

  return (
    <div className="bg-slate-900 dark:bg-black rounded-lg my-4 relative font-mono text-sm group">
      <button
        onClick={handleCopy}
        className="absolute top-2 end-2 p-2 bg-slate-700/50 rounded-md text-slate-300 hover:bg-slate-600/70 focus:outline-none focus:ring-2 focus:ring-sky-400 transition-all duration-200"
        aria-label="نسخ الكود"
      >
        {copied ? (
          <CheckIcon className="w-5 h-5 text-green-400" />
        ) : (
          <ClipboardIcon className="w-5 h-5" />
        )}
        <span className="absolute -start-20 top-1/2 -translate-y-1/2 bg-slate-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
          {copied ? 'تم النسخ!' : 'نسخ'}
        </span>
      </button>
      <pre className="p-4 overflow-x-auto" dir="ltr">
        <code className="text-white">{code}</code>
      </pre>
    </div>
  );
};

export default CodeBlock;

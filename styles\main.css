/* Main CSS for 3D Brain Tumor Segmentation Platform */

/* CSS Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Color Palette */
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --secondary-color: #10b981;
    --accent-color: #f59e0b;
    --danger-color: #ef4444;
    --warning-color: #f97316;
    
    /* Neutral Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #e2e8f0;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    
    /* Dark Mode Colors */
    --dark-bg-primary: #0f172a;
    --dark-bg-secondary: #1e293b;
    --dark-bg-tertiary: #334155;
    --dark-text-primary: #f1f5f9;
    --dark-text-secondary: #cbd5e1;
    --dark-text-muted: #94a3b8;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    
    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    overflow-x: hidden;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    color: white;
}

.loading-content {
    text-align: center;
    max-width: 400px;
    padding: var(--spacing-xl);
}

.brain-loader {
    position: relative;
    width: 80px;
    height: 60px;
    margin: 0 auto var(--spacing-lg);
}

.brain-hemisphere {
    position: absolute;
    width: 35px;
    height: 50px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
    animation: pulse 2s ease-in-out infinite;
}

.brain-hemisphere.left {
    left: 0;
    animation-delay: 0s;
}

.brain-hemisphere.right {
    right: 0;
    animation-delay: 0.5s;
}

@keyframes pulse {
    0%, 100% { opacity: 0.7; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.1); }
}

.loading-bar {
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: var(--radius-sm);
    overflow: hidden;
    margin-top: var(--spacing-lg);
}

.loading-progress {
    height: 100%;
    background: white;
    border-radius: var(--radius-sm);
    animation: loading 3s ease-in-out infinite;
}

@keyframes loading {
    0% { width: 0%; }
    50% { width: 70%; }
    100% { width: 100%; }
}

/* App Container */
.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.app-header {
    background: var(--bg-primary);
    border-bottom: 1px solid var(--bg-tertiary);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md) var(--spacing-xl);
    max-width: 1400px;
    margin: 0 auto;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.logo-section i {
    font-size: 2rem;
    color: var(--primary-color);
}

.logo-section h1 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
}

.subtitle {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
}

/* Navigation */
.main-nav {
    display: flex;
    gap: var(--spacing-sm);
}

.nav-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    background: transparent;
    color: var(--text-secondary);
    font-weight: 500;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    white-space: nowrap;
}

.nav-btn:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.nav-btn.active {
    background: var(--primary-color);
    color: white;
}

.nav-btn i {
    font-size: 1rem;
}

/* Progress Circle */
.user-progress {
    display: flex;
    align-items: center;
}

.progress-circle {
    position: relative;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: conic-gradient(var(--primary-color) 0deg, var(--bg-tertiary) 0deg);
    display: flex;
    align-items: center;
    justify-content: center;
}

.progress-text {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--text-primary);
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
}

/* Sidebar */
.sidebar {
    width: 280px;
    background: var(--bg-secondary);
    border-right: 1px solid var(--bg-tertiary);
    padding: var(--spacing-lg);
    overflow-y: auto;
    height: calc(100vh - 80px);
}

.sidebar-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.tools-panel h3 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.tool-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--bg-tertiary);
    background: var(--bg-primary);
    color: var(--text-secondary);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    margin-bottom: var(--spacing-sm);
    font-size: 0.875rem;
}

.tool-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.tool-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Content Panels */
.content-panels {
    flex: 1;
    padding: var(--spacing-xl);
    overflow-y: auto;
    height: calc(100vh - 80px);
}

.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

.section-header {
    margin-bottom: var(--spacing-xl);
}

.section-header h2 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.section-header p {
    font-size: 1.125rem;
    color: var(--text-secondary);
}

/* Overview Grid */
.overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
}

.feature-card {
    background: var(--bg-primary);
    border: 1px solid var(--bg-tertiary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    text-align: center;
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-sm);
}

.feature-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.card-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-lg);
}

.card-icon i {
    font-size: 1.5rem;
    color: white;
}

.feature-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.feature-card p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    line-height: 1.6;
}

.card-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-md);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.card-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

/* Footer */
.app-footer {
    background: var(--bg-secondary);
    border-top: 1px solid var(--bg-tertiary);
    padding: var(--spacing-lg) var(--spacing-xl);
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.footer-section h4 {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.footer-section p {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.progress-stats {
    display: flex;
    gap: var(--spacing-lg);
    font-size: 0.75rem;
    color: var(--text-secondary);
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: var(--spacing-md);
        padding: var(--spacing-md);
    }
    
    .main-nav {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .main-content {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
        height: auto;
        border-right: none;
        border-bottom: 1px solid var(--bg-tertiary);
    }
    
    .overview-grid {
        grid-template-columns: 1fr;
    }
    
    .footer-content {
        flex-direction: column;
        gap: var(--spacing-md);
        text-align: center;
    }
}

/* Menu Lists */
.menu-list {
    list-style: none;
    padding: 0;
}

.menu-list li {
    margin-bottom: var(--spacing-sm);
}

.menu-list a {
    display: block;
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.menu-list a:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

/* Error States */
.error-message {
    background: #fee2e2;
    color: #991b1b;
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    border: 1px solid #fecaca;
    margin: var(--spacing-md) 0;
}

.warning-message {
    background: #fef3c7;
    color: #92400e;
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    border: 1px solid #fde68a;
    margin: var(--spacing-md) 0;
}

.success-message {
    background: #dcfce7;
    color: #166534;
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    border: 1px solid #bbf7d0;
    margin: var(--spacing-md) 0;
}

/* Utility Classes */
.hidden { display: none !important; }
.visible { display: block !important; }
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.mt-auto { margin-top: auto; }
.mb-auto { margin-bottom: auto; }
.mx-auto { margin-left: auto; margin-right: auto; }
.w-full { width: 100%; }
.h-full { height: 100%; }

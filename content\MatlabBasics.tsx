
import React from 'react';
import SectionCard from '../components/SectionCard';
import CodeBlock from '../components/CodeBlock';

const MatlabBasics: React.FC = () => {
  return (
    <div>
      <h2 className="text-3xl font-extrabold text-slate-900 dark:text-white mb-6">🔧 أساسيات الماتلاب</h2>
      
      <SectionCard title="المتغيرات والعمليات الحسابية">
        <p>في الماتلاب، لا تحتاج إلى تعريف نوع المتغير. يمكنك ببساطة إسناد قيمة له. الفاصلة المنقوطة (;) في نهاية السطر تمنع عرض الناتج في نافذة الأوامر.</p>
        <CodeBlock code={`% تعريف المتغيرات\nx = 10;\ny = 5.5;\n\n% عمليات حسابية\nsum_val = x + y;       % الجمع\ndiff_val = x - y;      % الطرح\nprod_val = x * y;      % الضرب\ndiv_val = x / y;       % القسمة\npower_val = x ^ 2;     % القوة`} />
      </SectionCard>

      <SectionCard title="المصفوفات وعملياتها">
        <p>المصفوفات هي أساس الماتلاب. يمكنك إنشاؤها بسهولة وإجراء عمليات عليها عنصرًا بعنصر أو ككيان رياضي كامل.</p>
        <CodeBlock code={`% إنشاء مصفوفة 2x3\nA = [1, 2, 3; 4, 5, 6];\n\n% إنشاء متجه صفي\nvec_row = [10, 20, 30];\n\n% الوصول إلى عنصر (الصف الثاني, العمود الثالث)\nelement = A(2, 3);\n\n% ضرب المصفوفات عنصر بعنصر\nB = A .* 2;\n\n% الضرب الرياضي للمصفوفات (يتطلب توافق الأبعاد)\n% C = A * B'; % B' is the transpose of B`} />
      </SectionCard>

      <SectionCard title="الدوال والرسوم البيانية">
        <p>يحتوي الماتلاب على مكتبة ضخمة من الدوال الجاهزة، ويوفر أدوات قوية لإنشاء الرسوم البيانية لتصور البيانات.</p>
        <CodeBlock code={`% إنشاء بيانات للرسم\nx = 0:0.1:2*pi; % متجه من 0 إلى 2*pi بخطوة 0.1\ny = sin(x);\n\n% إنشاء رسم بياني ثنائي الأبعاد\nfigure; % إنشاء نافذة رسم جديدة\nplot(x, y, '-ro', 'LineWidth', 2);\n\ntitle('رسم دالة الجيب');\nxlabel('المحور السيني (x)');\nylabel('المحور الصادي (sin(x))');\ngrid on; % تفعيل الشبكة`} />
      </SectionCard>
    </div>
  );
};

export default MatlabBasics;
